2025-07-30 22:02:41,689 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-30 22:02:41,690 - __main__ - INFO - ================================================================================
2025-07-30 22:02:41,690 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-30 22:02:41,690 - __main__ - INFO - ================================================================================
2025-07-30 22:02:41,690 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-30 22:02:41,690 - __main__ - INFO - ----------------------------------------
2025-07-30 22:02:41,690 - __main__ - INFO - Running feature generator...
2025-07-30 22:02:42,246 - feature_generator - INFO - Found 2 CSV files to process
2025-07-30 22:02:42,344 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-30 22:02:46,794 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-30 22:02:46,795 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-30 22:03:13,595 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-30 22:03:13,596 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:03:14,965 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 62 features
2025-07-30 22:03:14,965 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-30 22:03:15,057 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-30 22:03:15,086 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-30 22:03:15,086 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-30 22:04:04,352 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-30 22:04:04,352 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:04:06,939 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 62 features
2025-07-30 22:04:06,939 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-30 22:04:06,939 - __main__ - INFO - Feature generator completed successfully
2025-07-30 22:04:06,939 - __main__ - INFO - Feature generation completed: 0 files
2025-07-30 22:04:06,939 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1753893246.txt
2025-07-30 22:04:06,939 - __main__ - INFO - ================================================================================
2025-07-30 22:04:06,939 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-30 22:04:06,947 - __main__ - INFO - ================================================================================
2025-07-30 22:20:59,643 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-30 22:20:59,643 - __main__ - INFO - ================================================================================
2025-07-30 22:20:59,643 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-30 22:20:59,643 - __main__ - INFO - ================================================================================
2025-07-30 22:20:59,643 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-30 22:20:59,643 - __main__ - INFO - ----------------------------------------
2025-07-30 22:20:59,643 - __main__ - INFO - Running feature generator...
2025-07-30 22:21:00,095 - feature_generator - INFO - Found 2 CSV files to process
2025-07-30 22:21:00,140 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-30 22:21:00,525 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-30 22:21:00,525 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-30 22:21:31,818 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-30 22:21:31,818 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:21:33,103 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 62 features
2025-07-30 22:21:33,103 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-30 22:21:33,260 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-30 22:21:33,289 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-30 22:21:33,289 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-30 22:22:47,426 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-30 22:22:47,426 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:22:52,606 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 62 features
2025-07-30 22:22:52,609 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-30 22:22:52,612 - __main__ - INFO - Feature generator completed successfully
2025-07-30 22:22:52,616 - __main__ - INFO - Feature generation completed: 0 files
2025-07-30 22:22:52,623 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1753894372.txt
2025-07-30 22:22:52,628 - __main__ - INFO - ================================================================================
2025-07-30 22:22:52,629 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-30 22:22:52,630 - __main__ - INFO - ================================================================================
2025-07-30 22:23:12,151 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-30 22:23:12,151 - __main__ - INFO - Running feature generator...
2025-07-30 22:23:12,543 - feature_generator - INFO - Found 2 CSV files to process
2025-07-30 22:23:12,584 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-30 22:23:12,601 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-30 22:23:12,602 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-30 22:23:12,612 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:23:31,288 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-30 22:23:31,288 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:23:32,789 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 63 features
2025-07-30 22:23:32,789 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-30 22:23:32,863 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-30 22:23:32,890 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-30 22:23:32,890 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-30 22:23:32,917 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:24:16,718 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-30 22:24:16,733 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:24:20,426 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 63 features
2025-07-30 22:24:20,427 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-30 22:24:20,430 - __main__ - INFO - Feature generator completed successfully
2025-07-30 22:39:38,438 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-30 22:39:38,438 - __main__ - INFO - Running feature generator...
2025-07-30 22:39:38,834 - feature_generator - INFO - Found 2 CSV files to process
2025-07-30 22:39:38,879 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-30 22:39:38,891 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-30 22:39:38,891 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-30 22:39:38,905 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:39:56,724 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-30 22:39:56,724 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:39:57,914 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 63 features
2025-07-30 22:39:57,914 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-30 22:39:57,984 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-30 22:39:58,017 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-30 22:39:58,017 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-30 22:39:58,042 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:40:48,763 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-30 22:40:48,763 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:40:52,296 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 63 features
2025-07-30 22:40:52,296 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-30 22:40:52,307 - __main__ - INFO - Feature generator completed successfully
2025-07-30 22:44:07,174 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-30 22:44:07,174 - __main__ - INFO - Running feature generator...
2025-07-30 22:44:07,579 - feature_generator - INFO - Found 2 CSV files to process
2025-07-30 22:44:07,627 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-30 22:44:07,633 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-30 22:44:07,633 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-30 22:44:07,656 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:44:24,739 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-30 22:44:24,739 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:44:26,651 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 63 features
2025-07-30 22:44:26,651 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-30 22:44:26,714 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-30 22:44:26,761 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-30 22:44:26,761 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-30 22:44:26,776 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:45:13,732 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-30 22:45:13,732 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:45:17,100 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 63 features
2025-07-30 22:45:17,100 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-30 22:45:17,100 - __main__ - INFO - Feature generator completed successfully
2025-07-30 22:50:20,394 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-30 22:50:20,394 - __main__ - INFO - Running feature generator...
2025-07-30 22:50:20,749 - feature_generator - INFO - Found 2 CSV files to process
2025-07-30 22:50:20,784 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-30 22:50:20,794 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-30 22:50:20,795 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-30 22:50:20,873 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:50:44,649 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-30 22:50:44,649 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:50:47,041 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 63 features
2025-07-30 22:50:47,041 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-30 22:50:47,120 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-30 22:50:47,135 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-30 22:50:47,135 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-30 22:50:47,185 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-30 22:51:42,266 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-30 22:51:42,266 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-30 22:51:45,323 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 63 features
2025-07-30 22:51:45,323 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-30 22:51:45,323 - __main__ - INFO - Feature generator completed successfully
2025-07-31 22:12:56,094 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-31 22:12:56,094 - __main__ - INFO - ================================================================================
2025-07-31 22:12:56,094 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-31 22:12:56,094 - __main__ - INFO - ================================================================================
2025-07-31 22:12:56,094 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-31 22:12:56,094 - __main__ - INFO - ----------------------------------------
2025-07-31 22:12:56,094 - __main__ - INFO - Running feature generator...
2025-07-31 22:12:56,596 - feature_generator - INFO - Found 65 CSV files to process
2025-07-31 22:12:56,752 - feature_generator - INFO - Dropped 'volume' column from Bankex_1.csv
2025-07-31 22:12:56,809 - feature_generator - INFO - Loaded 99915 rows from Bankex_1.csv
2025-07-31 22:12:56,809 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:12:56,950 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:14:26,510 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:14:26,510 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:14:33,538 - feature_generator - INFO - Processed Bankex_1.csv: 99716 rows, 63 features
2025-07-31 22:14:33,538 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_1.csv
2025-07-31 22:14:33,567 - feature_generator - INFO - Dropped 'volume' column from Bankex_10.csv
2025-07-31 22:14:33,573 - feature_generator - INFO - Loaded 10127 rows from Bankex_10.csv
2025-07-31 22:14:33,574 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:14:33,580 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:14:42,944 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:14:42,945 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:14:43,599 - feature_generator - INFO - Processed Bankex_10.csv: 9928 rows, 63 features
2025-07-31 22:14:43,599 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_10.csv
2025-07-31 22:14:43,615 - feature_generator - INFO - Dropped 'volume' column from Bankex_120.csv
2025-07-31 22:14:43,615 - feature_generator - INFO - Loaded 1067 rows from Bankex_120.csv
2025-07-31 22:14:43,615 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:14:43,626 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:14:44,718 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:14:44,718 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:14:44,773 - feature_generator - INFO - Processed Bankex_120.csv: 868 rows, 63 features
2025-07-31 22:14:44,773 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_120.csv
2025-07-31 22:14:44,798 - feature_generator - INFO - Dropped 'volume' column from Bankex_15.csv
2025-07-31 22:14:44,798 - feature_generator - INFO - Loaded 6661 rows from Bankex_15.csv
2025-07-31 22:14:44,798 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:14:44,798 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:14:50,683 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:14:50,684 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:14:51,084 - feature_generator - INFO - Processed Bankex_15.csv: 6462 rows, 63 features
2025-07-31 22:14:51,084 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_15.csv
2025-07-31 22:14:51,084 - feature_generator - INFO - Dropped 'volume' column from Bankex_180.csv
2025-07-31 22:14:51,112 - feature_generator - INFO - Loaded 802 rows from Bankex_180.csv
2025-07-31 22:14:51,112 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:14:51,121 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:14:52,065 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:14:52,065 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:14:52,125 - feature_generator - INFO - Processed Bankex_180.csv: 603 rows, 63 features
2025-07-31 22:14:52,125 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_180.csv
2025-07-31 22:14:52,213 - feature_generator - INFO - Dropped 'volume' column from Bankex_2.csv
2025-07-31 22:14:52,233 - feature_generator - INFO - Loaded 50093 rows from Bankex_2.csv
2025-07-31 22:14:52,233 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:14:52,266 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:15:39,673 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:15:39,673 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:15:42,713 - feature_generator - INFO - Processed Bankex_2.csv: 49894 rows, 63 features
2025-07-31 22:15:42,713 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_2.csv
2025-07-31 22:15:42,713 - feature_generator - INFO - Dropped 'volume' column from Bankex_20.csv
2025-07-31 22:15:42,735 - feature_generator - INFO - Loaded 5065 rows from Bankex_20.csv
2025-07-31 22:15:42,743 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:15:42,747 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:15:47,167 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:15:47,167 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:15:47,458 - feature_generator - INFO - Processed Bankex_20.csv: 4866 rows, 63 features
2025-07-31 22:15:47,458 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_20.csv
2025-07-31 22:15:47,474 - feature_generator - INFO - Dropped 'volume' column from Bankex_240.csv
2025-07-31 22:15:47,482 - feature_generator - INFO - Loaded 534 rows from Bankex_240.csv
2025-07-31 22:15:47,483 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:15:47,484 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:15:48,015 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:15:48,015 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:15:48,052 - feature_generator - INFO - Processed Bankex_240.csv: 335 rows, 63 features
2025-07-31 22:15:48,056 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_240.csv
2025-07-31 22:15:48,109 - feature_generator - INFO - Dropped 'volume' column from Bankex_3.csv
2025-07-31 22:15:48,131 - feature_generator - INFO - Loaded 33305 rows from Bankex_3.csv
2025-07-31 22:15:48,131 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:15:48,148 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:16:16,627 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:16:16,627 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:16:18,675 - feature_generator - INFO - Processed Bankex_3.csv: 33106 rows, 63 features
2025-07-31 22:16:18,675 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_3.csv
2025-07-31 22:16:18,695 - feature_generator - INFO - Dropped 'volume' column from Bankex_30.csv
2025-07-31 22:16:18,700 - feature_generator - INFO - Loaded 3466 rows from Bankex_30.csv
2025-07-31 22:16:18,701 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:16:18,706 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:16:21,747 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:16:21,762 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:16:21,966 - feature_generator - INFO - Processed Bankex_30.csv: 3267 rows, 63 features
2025-07-31 22:16:21,966 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_30.csv
2025-07-31 22:16:21,966 - feature_generator - INFO - Dropped 'volume' column from Bankex_45.csv
2025-07-31 22:16:21,982 - feature_generator - INFO - Loaded 2399 rows from Bankex_45.csv
2025-07-31 22:16:21,982 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:16:21,990 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:16:24,306 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:16:24,306 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:16:24,445 - feature_generator - INFO - Processed Bankex_45.csv: 2200 rows, 63 features
2025-07-31 22:16:24,445 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_45.csv
2025-07-31 22:16:24,486 - feature_generator - INFO - Dropped 'volume' column from Bankex_5.csv
2025-07-31 22:16:24,496 - feature_generator - INFO - Loaded 19983 rows from Bankex_5.csv
2025-07-31 22:16:24,497 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:16:24,510 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:16:42,897 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:16:42,897 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:16:44,176 - feature_generator - INFO - Processed Bankex_5.csv: 19784 rows, 63 features
2025-07-31 22:16:44,176 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_5.csv
2025-07-31 22:16:44,193 - feature_generator - INFO - Dropped 'volume' column from Bankex_60.csv
2025-07-31 22:16:44,196 - feature_generator - INFO - Loaded 1867 rows from Bankex_60.csv
2025-07-31 22:16:44,197 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:16:44,198 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:16:46,085 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:16:46,085 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:16:46,203 - feature_generator - INFO - Processed Bankex_60.csv: 1668 rows, 63 features
2025-07-31 22:16:46,203 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_60.csv
2025-07-31 22:16:46,325 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_1.csv
2025-07-31 22:16:46,378 - feature_generator - INFO - Loaded 99915 rows from Bank_Nifty_1.csv
2025-07-31 22:16:46,379 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:16:46,423 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:18:12,120 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:18:12,120 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:18:17,931 - feature_generator - INFO - Processed Bank_Nifty_1.csv: 99716 rows, 63 features
2025-07-31 22:18:17,931 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_1.csv
2025-07-31 22:18:17,946 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_10.csv
2025-07-31 22:18:17,968 - feature_generator - INFO - Loaded 10127 rows from Bank_Nifty_10.csv
2025-07-31 22:18:17,970 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:18:17,980 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:18:27,304 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:18:27,304 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:18:27,923 - feature_generator - INFO - Processed Bank_Nifty_10.csv: 9928 rows, 63 features
2025-07-31 22:18:27,924 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_10.csv
2025-07-31 22:18:27,933 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_120.csv
2025-07-31 22:18:27,941 - feature_generator - INFO - Loaded 1067 rows from Bank_Nifty_120.csv
2025-07-31 22:18:27,941 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:18:27,955 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:18:29,127 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:18:29,141 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:18:29,250 - feature_generator - INFO - Processed Bank_Nifty_120.csv: 868 rows, 63 features
2025-07-31 22:18:29,250 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_120.csv
2025-07-31 22:18:29,281 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_15.csv
2025-07-31 22:18:29,288 - feature_generator - INFO - Loaded 6661 rows from Bank_Nifty_15.csv
2025-07-31 22:18:29,302 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:18:29,315 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:18:35,596 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:18:35,596 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:18:36,005 - feature_generator - INFO - Processed Bank_Nifty_15.csv: 6462 rows, 63 features
2025-07-31 22:18:36,005 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_15.csv
2025-07-31 22:18:36,020 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_180.csv
2025-07-31 22:18:36,030 - feature_generator - INFO - Loaded 802 rows from Bank_Nifty_180.csv
2025-07-31 22:18:36,030 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:18:36,039 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:18:36,901 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:18:36,901 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:18:36,957 - feature_generator - INFO - Processed Bank_Nifty_180.csv: 603 rows, 63 features
2025-07-31 22:18:36,957 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_180.csv
2025-07-31 22:18:37,035 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_2.csv
2025-07-31 22:18:37,063 - feature_generator - INFO - Loaded 50093 rows from Bank_Nifty_2.csv
2025-07-31 22:18:37,063 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:18:37,097 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:19:25,836 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:19:25,836 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:19:29,057 - feature_generator - INFO - Processed Bank_Nifty_2.csv: 49894 rows, 63 features
2025-07-31 22:19:29,058 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_2.csv
2025-07-31 22:19:29,072 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_20.csv
2025-07-31 22:19:29,079 - feature_generator - INFO - Loaded 5065 rows from Bank_Nifty_20.csv
2025-07-31 22:19:29,081 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:19:29,092 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:19:33,767 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:19:33,767 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:19:34,067 - feature_generator - INFO - Processed Bank_Nifty_20.csv: 4866 rows, 63 features
2025-07-31 22:19:34,067 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_20.csv
2025-07-31 22:19:34,067 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_240.csv
2025-07-31 22:19:34,086 - feature_generator - INFO - Loaded 534 rows from Bank_Nifty_240.csv
2025-07-31 22:19:34,087 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:19:34,095 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:19:34,664 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:19:34,664 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:19:34,684 - feature_generator - INFO - Processed Bank_Nifty_240.csv: 335 rows, 63 features
2025-07-31 22:19:34,684 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_240.csv
2025-07-31 22:19:34,743 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_3.csv
2025-07-31 22:19:34,756 - feature_generator - INFO - Loaded 33305 rows from Bank_Nifty_3.csv
2025-07-31 22:19:34,756 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:19:34,777 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:20:04,625 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:20:04,625 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:20:06,604 - feature_generator - INFO - Processed Bank_Nifty_3.csv: 33106 rows, 63 features
2025-07-31 22:20:06,604 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_3.csv
2025-07-31 22:20:06,620 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_30.csv
2025-07-31 22:20:06,631 - feature_generator - INFO - Loaded 3466 rows from Bank_Nifty_30.csv
2025-07-31 22:20:06,632 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:20:06,642 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:20:09,985 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:20:09,985 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:20:10,205 - feature_generator - INFO - Processed Bank_Nifty_30.csv: 3267 rows, 63 features
2025-07-31 22:20:10,205 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_30.csv
2025-07-31 22:20:10,205 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_45.csv
2025-07-31 22:20:10,220 - feature_generator - INFO - Loaded 2399 rows from Bank_Nifty_45.csv
2025-07-31 22:20:10,220 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:20:10,230 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:20:12,540 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:20:12,540 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:20:12,695 - feature_generator - INFO - Processed Bank_Nifty_45.csv: 2200 rows, 63 features
2025-07-31 22:20:12,695 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_45.csv
2025-07-31 22:20:12,727 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-31 22:20:12,741 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-31 22:20:12,742 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:20:12,754 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:20:30,191 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:20:30,191 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:20:31,412 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 63 features
2025-07-31 22:20:31,412 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-31 22:20:31,412 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_60.csv
2025-07-31 22:20:31,429 - feature_generator - INFO - Loaded 1867 rows from Bank_Nifty_60.csv
2025-07-31 22:20:31,430 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:20:31,438 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:20:33,237 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:20:33,237 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:20:33,372 - feature_generator - INFO - Processed Bank_Nifty_60.csv: 1668 rows, 63 features
2025-07-31 22:20:33,372 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_60.csv
2025-07-31 22:20:33,496 - feature_generator - INFO - Dropped 'volume' column from Finnifty_1.csv
2025-07-31 22:20:33,537 - feature_generator - INFO - Loaded 99915 rows from Finnifty_1.csv
2025-07-31 22:20:33,545 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:20:33,590 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:22:03,097 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:22:03,129 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:22:09,364 - feature_generator - INFO - Processed Finnifty_1.csv: 99716 rows, 63 features
2025-07-31 22:22:09,364 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_1.csv
2025-07-31 22:22:09,383 - feature_generator - INFO - Dropped 'volume' column from Finnifty_10.csv
2025-07-31 22:22:09,393 - feature_generator - INFO - Loaded 10127 rows from Finnifty_10.csv
2025-07-31 22:22:09,393 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:22:09,405 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:22:18,232 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:22:18,232 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:22:18,856 - feature_generator - INFO - Processed Finnifty_10.csv: 9928 rows, 63 features
2025-07-31 22:22:18,856 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_10.csv
2025-07-31 22:22:18,856 - feature_generator - INFO - Dropped 'volume' column from Finnifty_120.csv
2025-07-31 22:22:18,872 - feature_generator - INFO - Loaded 1067 rows from Finnifty_120.csv
2025-07-31 22:22:18,873 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:22:18,876 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:22:19,883 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:22:19,883 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:22:19,938 - feature_generator - INFO - Processed Finnifty_120.csv: 868 rows, 63 features
2025-07-31 22:22:19,938 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_120.csv
2025-07-31 22:22:19,964 - feature_generator - INFO - Dropped 'volume' column from Finnifty_15.csv
2025-07-31 22:22:19,970 - feature_generator - INFO - Loaded 6661 rows from Finnifty_15.csv
2025-07-31 22:22:19,971 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:22:19,980 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:22:25,983 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:22:25,983 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:22:26,389 - feature_generator - INFO - Processed Finnifty_15.csv: 6462 rows, 63 features
2025-07-31 22:22:26,389 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_15.csv
2025-07-31 22:22:26,389 - feature_generator - INFO - Dropped 'volume' column from Finnifty_180.csv
2025-07-31 22:22:26,411 - feature_generator - INFO - Loaded 802 rows from Finnifty_180.csv
2025-07-31 22:22:26,411 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:22:26,424 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:22:27,163 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:22:27,163 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:22:27,211 - feature_generator - INFO - Processed Finnifty_180.csv: 603 rows, 63 features
2025-07-31 22:22:27,211 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_180.csv
2025-07-31 22:22:27,285 - feature_generator - INFO - Dropped 'volume' column from Finnifty_2.csv
2025-07-31 22:22:27,296 - feature_generator - INFO - Loaded 50093 rows from Finnifty_2.csv
2025-07-31 22:22:27,296 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:22:27,326 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:23:15,080 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:23:15,080 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:23:18,096 - feature_generator - INFO - Processed Finnifty_2.csv: 49894 rows, 63 features
2025-07-31 22:23:18,096 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_2.csv
2025-07-31 22:23:18,119 - feature_generator - INFO - Dropped 'volume' column from Finnifty_20.csv
2025-07-31 22:23:18,127 - feature_generator - INFO - Loaded 5065 rows from Finnifty_20.csv
2025-07-31 22:23:18,128 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:23:18,136 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:23:22,881 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:23:22,881 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:23:23,181 - feature_generator - INFO - Processed Finnifty_20.csv: 4866 rows, 63 features
2025-07-31 22:23:23,181 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_20.csv
2025-07-31 22:23:23,181 - feature_generator - INFO - Dropped 'volume' column from Finnifty_240.csv
2025-07-31 22:23:23,200 - feature_generator - INFO - Loaded 534 rows from Finnifty_240.csv
2025-07-31 22:23:23,200 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:23:23,200 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:23:23,691 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:23:23,691 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:23:23,720 - feature_generator - INFO - Processed Finnifty_240.csv: 335 rows, 63 features
2025-07-31 22:23:23,720 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_240.csv
2025-07-31 22:23:23,766 - feature_generator - INFO - Dropped 'volume' column from Finnifty_3.csv
2025-07-31 22:23:23,786 - feature_generator - INFO - Loaded 33305 rows from Finnifty_3.csv
2025-07-31 22:23:23,787 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:23:23,800 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:23:53,813 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:23:53,813 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:23:56,084 - feature_generator - INFO - Processed Finnifty_3.csv: 33106 rows, 63 features
2025-07-31 22:23:56,084 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_3.csv
2025-07-31 22:23:56,098 - feature_generator - INFO - Dropped 'volume' column from Finnifty_30.csv
2025-07-31 22:23:56,136 - feature_generator - INFO - Loaded 3466 rows from Finnifty_30.csv
2025-07-31 22:23:56,148 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:23:56,163 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:24:00,205 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:24:00,205 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:24:00,411 - feature_generator - INFO - Processed Finnifty_30.csv: 3267 rows, 63 features
2025-07-31 22:24:00,411 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_30.csv
2025-07-31 22:24:00,426 - feature_generator - INFO - Dropped 'volume' column from Finnifty_45.csv
2025-07-31 22:24:00,439 - feature_generator - INFO - Loaded 2399 rows from Finnifty_45.csv
2025-07-31 22:24:00,439 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:24:00,439 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:24:02,718 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:24:02,718 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:24:02,879 - feature_generator - INFO - Processed Finnifty_45.csv: 2200 rows, 63 features
2025-07-31 22:24:02,880 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_45.csv
2025-07-31 22:24:02,909 - feature_generator - INFO - Dropped 'volume' column from Finnifty_5.csv
2025-07-31 22:24:02,917 - feature_generator - INFO - Loaded 19983 rows from Finnifty_5.csv
2025-07-31 22:24:02,917 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:24:02,935 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:24:20,702 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:24:20,702 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:24:21,928 - feature_generator - INFO - Processed Finnifty_5.csv: 19784 rows, 63 features
2025-07-31 22:24:21,928 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_5.csv
2025-07-31 22:24:21,944 - feature_generator - INFO - Dropped 'volume' column from Finnifty_60.csv
2025-07-31 22:24:21,947 - feature_generator - INFO - Loaded 1867 rows from Finnifty_60.csv
2025-07-31 22:24:21,948 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:24:21,953 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:24:23,818 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:24:23,819 - feature_generator - INFO - Generated 58 features for DataFrame
2025-07-31 22:24:23,942 - feature_generator - INFO - Processed Finnifty_60.csv: 1668 rows, 63 features
2025-07-31 22:24:23,942 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_60.csv
2025-07-31 22:24:24,065 - feature_generator - INFO - Dropped 'volume' column from Nifty_1.csv
2025-07-31 22:24:24,109 - feature_generator - INFO - Loaded 99915 rows from Nifty_1.csv
2025-07-31 22:24:24,109 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:24:24,154 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:27:11,565 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-31 22:27:11,565 - __main__ - INFO - ================================================================================
2025-07-31 22:27:11,565 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-31 22:27:11,565 - __main__ - INFO - ================================================================================
2025-07-31 22:27:11,565 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-31 22:27:11,565 - __main__ - INFO - ----------------------------------------
2025-07-31 22:27:11,565 - __main__ - INFO - Running feature generator...
2025-07-31 22:27:11,989 - __main__ - ERROR - Feature generation step failed: name 'PandasTAIndicators' is not defined
2025-07-31 22:27:46,792 - __main__ - INFO - DataProcessingPipeline initialized
2025-07-31 22:27:46,792 - __main__ - INFO - ================================================================================
2025-07-31 22:27:46,792 - __main__ - INFO - STARTING COMPLETE DATA PROCESSING PIPELINE
2025-07-31 22:27:46,792 - __main__ - INFO - ================================================================================
2025-07-31 22:27:46,792 - __main__ - INFO - STEP 1: FEATURE GENERATION
2025-07-31 22:27:46,792 - __main__ - INFO - ----------------------------------------
2025-07-31 22:27:46,792 - __main__ - INFO - Running feature generator...
2025-07-31 22:27:47,217 - feature_generator - INFO - Found 65 CSV files to process
2025-07-31 22:27:47,365 - feature_generator - INFO - Dropped 'volume' column from Bankex_1.csv
2025-07-31 22:27:47,425 - feature_generator - INFO - Loaded 99915 rows from Bankex_1.csv
2025-07-31 22:27:47,425 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:27:47,566 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:29:17,846 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:29:17,846 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:29:24,796 - feature_generator - INFO - Processed Bankex_1.csv: 99716 rows, 53 features
2025-07-31 22:29:24,875 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_1.csv
2025-07-31 22:29:24,937 - feature_generator - INFO - Dropped 'volume' column from Bankex_10.csv
2025-07-31 22:29:25,015 - feature_generator - INFO - Loaded 10127 rows from Bankex_10.csv
2025-07-31 22:29:25,078 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:29:25,125 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:29:37,212 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:29:37,212 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:29:37,794 - feature_generator - INFO - Processed Bankex_10.csv: 9928 rows, 53 features
2025-07-31 22:29:37,794 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_10.csv
2025-07-31 22:29:37,794 - feature_generator - INFO - Dropped 'volume' column from Bankex_120.csv
2025-07-31 22:29:37,809 - feature_generator - INFO - Loaded 1067 rows from Bankex_120.csv
2025-07-31 22:29:37,810 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:29:37,812 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:29:38,796 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:29:38,796 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:29:38,853 - feature_generator - INFO - Processed Bankex_120.csv: 868 rows, 53 features
2025-07-31 22:29:38,853 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_120.csv
2025-07-31 22:29:38,869 - feature_generator - INFO - Dropped 'volume' column from Bankex_15.csv
2025-07-31 22:29:38,883 - feature_generator - INFO - Loaded 6661 rows from Bankex_15.csv
2025-07-31 22:29:38,884 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:29:38,890 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:29:45,127 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:29:45,127 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:29:45,495 - feature_generator - INFO - Processed Bankex_15.csv: 6462 rows, 53 features
2025-07-31 22:29:45,496 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_15.csv
2025-07-31 22:29:45,501 - feature_generator - INFO - Dropped 'volume' column from Bankex_180.csv
2025-07-31 22:29:45,507 - feature_generator - INFO - Loaded 802 rows from Bankex_180.csv
2025-07-31 22:29:45,507 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:29:45,511 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:29:46,287 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:29:46,287 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:29:46,338 - feature_generator - INFO - Processed Bankex_180.csv: 603 rows, 53 features
2025-07-31 22:29:46,339 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_180.csv
2025-07-31 22:29:46,407 - feature_generator - INFO - Dropped 'volume' column from Bankex_2.csv
2025-07-31 22:29:46,429 - feature_generator - INFO - Loaded 50093 rows from Bankex_2.csv
2025-07-31 22:29:46,429 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:29:46,453 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:30:36,201 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:30:36,201 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:30:39,526 - feature_generator - INFO - Processed Bankex_2.csv: 49894 rows, 53 features
2025-07-31 22:30:39,527 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_2.csv
2025-07-31 22:30:39,539 - feature_generator - INFO - Dropped 'volume' column from Bankex_20.csv
2025-07-31 22:30:39,546 - feature_generator - INFO - Loaded 5065 rows from Bankex_20.csv
2025-07-31 22:30:39,546 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:30:39,556 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:30:44,559 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:30:44,559 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:30:44,808 - feature_generator - INFO - Processed Bankex_20.csv: 4866 rows, 53 features
2025-07-31 22:30:44,808 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_20.csv
2025-07-31 22:30:44,824 - feature_generator - INFO - Dropped 'volume' column from Bankex_240.csv
2025-07-31 22:30:44,824 - feature_generator - INFO - Loaded 534 rows from Bankex_240.csv
2025-07-31 22:30:44,824 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:30:44,824 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:30:45,309 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:30:45,309 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:30:45,325 - feature_generator - INFO - Processed Bankex_240.csv: 335 rows, 53 features
2025-07-31 22:30:45,325 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_240.csv
2025-07-31 22:30:45,383 - feature_generator - INFO - Dropped 'volume' column from Bankex_3.csv
2025-07-31 22:30:45,400 - feature_generator - INFO - Loaded 33305 rows from Bankex_3.csv
2025-07-31 22:30:45,400 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:30:45,414 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:31:13,380 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:31:13,380 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:31:15,055 - feature_generator - INFO - Processed Bankex_3.csv: 33106 rows, 53 features
2025-07-31 22:31:15,055 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_3.csv
2025-07-31 22:31:15,071 - feature_generator - INFO - Dropped 'volume' column from Bankex_30.csv
2025-07-31 22:31:15,071 - feature_generator - INFO - Loaded 3466 rows from Bankex_30.csv
2025-07-31 22:31:15,071 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:31:15,071 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:31:18,130 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:31:18,130 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:31:18,318 - feature_generator - INFO - Processed Bankex_30.csv: 3267 rows, 53 features
2025-07-31 22:31:18,318 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_30.csv
2025-07-31 22:31:18,318 - feature_generator - INFO - Dropped 'volume' column from Bankex_45.csv
2025-07-31 22:31:18,331 - feature_generator - INFO - Loaded 2399 rows from Bankex_45.csv
2025-07-31 22:31:18,332 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:31:18,336 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:31:20,485 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:31:20,485 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:31:20,610 - feature_generator - INFO - Processed Bankex_45.csv: 2200 rows, 53 features
2025-07-31 22:31:20,610 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_45.csv
2025-07-31 22:31:20,626 - feature_generator - INFO - Dropped 'volume' column from Bankex_5.csv
2025-07-31 22:31:20,641 - feature_generator - INFO - Loaded 19983 rows from Bankex_5.csv
2025-07-31 22:31:20,641 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:31:20,657 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:31:37,623 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:31:37,623 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:31:38,679 - feature_generator - INFO - Processed Bankex_5.csv: 19784 rows, 53 features
2025-07-31 22:31:38,679 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_5.csv
2025-07-31 22:31:38,679 - feature_generator - INFO - Dropped 'volume' column from Bankex_60.csv
2025-07-31 22:31:38,696 - feature_generator - INFO - Loaded 1867 rows from Bankex_60.csv
2025-07-31 22:31:38,697 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:31:38,699 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:31:40,499 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:31:40,499 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:31:40,612 - feature_generator - INFO - Processed Bankex_60.csv: 1668 rows, 53 features
2025-07-31 22:31:40,612 - feature_generator - INFO - Replaced existing file: data\final\features_Bankex_60.csv
2025-07-31 22:31:40,736 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_1.csv
2025-07-31 22:31:40,786 - feature_generator - INFO - Loaded 99915 rows from Bank_Nifty_1.csv
2025-07-31 22:31:40,786 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:31:40,826 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:33:05,000 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:33:05,000 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:33:10,131 - feature_generator - INFO - Processed Bank_Nifty_1.csv: 99716 rows, 53 features
2025-07-31 22:33:10,131 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_1.csv
2025-07-31 22:33:10,153 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_10.csv
2025-07-31 22:33:10,162 - feature_generator - INFO - Loaded 10127 rows from Bank_Nifty_10.csv
2025-07-31 22:33:10,162 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:33:10,184 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:33:18,836 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:33:18,836 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:33:19,373 - feature_generator - INFO - Processed Bank_Nifty_10.csv: 9928 rows, 53 features
2025-07-31 22:33:19,373 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_10.csv
2025-07-31 22:33:19,373 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_120.csv
2025-07-31 22:33:19,390 - feature_generator - INFO - Loaded 1067 rows from Bank_Nifty_120.csv
2025-07-31 22:33:19,391 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:33:19,394 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:33:20,416 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:33:20,416 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:33:20,471 - feature_generator - INFO - Processed Bank_Nifty_120.csv: 868 rows, 53 features
2025-07-31 22:33:20,471 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_120.csv
2025-07-31 22:33:20,493 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_15.csv
2025-07-31 22:33:20,501 - feature_generator - INFO - Loaded 6661 rows from Bank_Nifty_15.csv
2025-07-31 22:33:20,501 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:33:20,512 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:33:26,287 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:33:26,287 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:33:26,643 - feature_generator - INFO - Processed Bank_Nifty_15.csv: 6462 rows, 53 features
2025-07-31 22:33:26,643 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_15.csv
2025-07-31 22:33:26,643 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_180.csv
2025-07-31 22:33:26,654 - feature_generator - INFO - Loaded 802 rows from Bank_Nifty_180.csv
2025-07-31 22:33:26,656 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:33:26,658 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:33:27,390 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:33:27,390 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:33:27,437 - feature_generator - INFO - Processed Bank_Nifty_180.csv: 603 rows, 53 features
2025-07-31 22:33:27,439 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_180.csv
2025-07-31 22:33:27,516 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_2.csv
2025-07-31 22:33:27,522 - feature_generator - INFO - Loaded 50093 rows from Bank_Nifty_2.csv
2025-07-31 22:33:27,522 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:33:27,569 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:34:14,384 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:34:14,384 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:34:16,984 - feature_generator - INFO - Processed Bank_Nifty_2.csv: 49894 rows, 53 features
2025-07-31 22:34:16,984 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_2.csv
2025-07-31 22:34:17,000 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_20.csv
2025-07-31 22:34:17,016 - feature_generator - INFO - Loaded 5065 rows from Bank_Nifty_20.csv
2025-07-31 22:34:17,017 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:34:17,018 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:34:21,482 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:34:21,482 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:34:21,752 - feature_generator - INFO - Processed Bank_Nifty_20.csv: 4866 rows, 53 features
2025-07-31 22:34:21,752 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_20.csv
2025-07-31 22:34:21,768 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_240.csv
2025-07-31 22:34:21,768 - feature_generator - INFO - Loaded 534 rows from Bank_Nifty_240.csv
2025-07-31 22:34:21,768 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:34:21,780 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:34:22,273 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:34:22,273 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:34:22,294 - feature_generator - INFO - Processed Bank_Nifty_240.csv: 335 rows, 53 features
2025-07-31 22:34:22,295 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_240.csv
2025-07-31 22:34:22,339 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_3.csv
2025-07-31 22:34:22,362 - feature_generator - INFO - Loaded 33305 rows from Bank_Nifty_3.csv
2025-07-31 22:34:22,363 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:34:22,378 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:34:50,276 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:34:50,276 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:34:52,018 - feature_generator - INFO - Processed Bank_Nifty_3.csv: 33106 rows, 53 features
2025-07-31 22:34:52,018 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_3.csv
2025-07-31 22:34:52,034 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_30.csv
2025-07-31 22:34:52,044 - feature_generator - INFO - Loaded 3466 rows from Bank_Nifty_30.csv
2025-07-31 22:34:52,044 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:34:52,048 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:34:55,237 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:34:55,237 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:34:55,433 - feature_generator - INFO - Processed Bank_Nifty_30.csv: 3267 rows, 53 features
2025-07-31 22:34:55,433 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_30.csv
2025-07-31 22:34:55,448 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_45.csv
2025-07-31 22:34:55,458 - feature_generator - INFO - Loaded 2399 rows from Bank_Nifty_45.csv
2025-07-31 22:34:55,459 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:34:55,462 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:34:57,690 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:34:57,690 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:34:57,836 - feature_generator - INFO - Processed Bank_Nifty_45.csv: 2200 rows, 53 features
2025-07-31 22:34:57,836 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_45.csv
2025-07-31 22:34:57,878 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_5.csv
2025-07-31 22:34:57,896 - feature_generator - INFO - Loaded 19983 rows from Bank_Nifty_5.csv
2025-07-31 22:34:57,897 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:34:57,910 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:35:14,776 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:35:14,776 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:35:15,847 - feature_generator - INFO - Processed Bank_Nifty_5.csv: 19784 rows, 53 features
2025-07-31 22:35:15,847 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_5.csv
2025-07-31 22:35:15,847 - feature_generator - INFO - Dropped 'volume' column from Bank_Nifty_60.csv
2025-07-31 22:35:15,861 - feature_generator - INFO - Loaded 1867 rows from Bank_Nifty_60.csv
2025-07-31 22:35:15,862 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:35:15,867 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:35:17,634 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:35:17,634 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:35:17,741 - feature_generator - INFO - Processed Bank_Nifty_60.csv: 1668 rows, 53 features
2025-07-31 22:35:17,741 - feature_generator - INFO - Replaced existing file: data\final\features_Bank_Nifty_60.csv
2025-07-31 22:35:17,862 - feature_generator - INFO - Dropped 'volume' column from Finnifty_1.csv
2025-07-31 22:35:17,916 - feature_generator - INFO - Loaded 99915 rows from Finnifty_1.csv
2025-07-31 22:35:17,916 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:35:17,955 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:36:41,494 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:36:41,494 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:36:46,605 - feature_generator - INFO - Processed Finnifty_1.csv: 99716 rows, 53 features
2025-07-31 22:36:46,605 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_1.csv
2025-07-31 22:36:46,633 - feature_generator - INFO - Dropped 'volume' column from Finnifty_10.csv
2025-07-31 22:36:46,637 - feature_generator - INFO - Loaded 10127 rows from Finnifty_10.csv
2025-07-31 22:36:46,637 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:36:46,652 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:36:55,217 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:36:55,217 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:36:55,748 - feature_generator - INFO - Processed Finnifty_10.csv: 9928 rows, 53 features
2025-07-31 22:36:55,748 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_10.csv
2025-07-31 22:36:55,762 - feature_generator - INFO - Dropped 'volume' column from Finnifty_120.csv
2025-07-31 22:36:55,766 - feature_generator - INFO - Loaded 1067 rows from Finnifty_120.csv
2025-07-31 22:36:55,766 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:36:55,771 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:36:56,745 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:36:56,745 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:36:56,799 - feature_generator - INFO - Processed Finnifty_120.csv: 868 rows, 53 features
2025-07-31 22:36:56,799 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_120.csv
2025-07-31 22:36:56,819 - feature_generator - INFO - Dropped 'volume' column from Finnifty_15.csv
2025-07-31 22:36:56,834 - feature_generator - INFO - Loaded 6661 rows from Finnifty_15.csv
2025-07-31 22:36:56,835 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:36:56,849 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:37:02,800 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:37:02,800 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:37:03,143 - feature_generator - INFO - Processed Finnifty_15.csv: 6462 rows, 53 features
2025-07-31 22:37:03,143 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_15.csv
2025-07-31 22:37:03,143 - feature_generator - INFO - Dropped 'volume' column from Finnifty_180.csv
2025-07-31 22:37:03,164 - feature_generator - INFO - Loaded 802 rows from Finnifty_180.csv
2025-07-31 22:37:03,165 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:37:03,173 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:37:03,892 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:37:03,892 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:37:03,939 - feature_generator - INFO - Processed Finnifty_180.csv: 603 rows, 53 features
2025-07-31 22:37:03,940 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_180.csv
2025-07-31 22:37:04,015 - feature_generator - INFO - Dropped 'volume' column from Finnifty_2.csv
2025-07-31 22:37:04,024 - feature_generator - INFO - Loaded 50093 rows from Finnifty_2.csv
2025-07-31 22:37:04,024 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:37:04,051 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:37:50,414 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:37:50,414 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:37:53,028 - feature_generator - INFO - Processed Finnifty_2.csv: 49894 rows, 53 features
2025-07-31 22:37:53,028 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_2.csv
2025-07-31 22:37:53,048 - feature_generator - INFO - Dropped 'volume' column from Finnifty_20.csv
2025-07-31 22:37:53,056 - feature_generator - INFO - Loaded 5065 rows from Finnifty_20.csv
2025-07-31 22:37:53,057 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:37:53,061 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:37:57,661 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:37:57,661 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:37:57,931 - feature_generator - INFO - Processed Finnifty_20.csv: 4866 rows, 53 features
2025-07-31 22:37:57,931 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_20.csv
2025-07-31 22:37:57,947 - feature_generator - INFO - Dropped 'volume' column from Finnifty_240.csv
2025-07-31 22:37:57,957 - feature_generator - INFO - Loaded 534 rows from Finnifty_240.csv
2025-07-31 22:37:57,957 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:37:57,964 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:37:58,444 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:37:58,444 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:37:58,470 - feature_generator - INFO - Processed Finnifty_240.csv: 335 rows, 53 features
2025-07-31 22:37:58,471 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_240.csv
2025-07-31 22:37:58,521 - feature_generator - INFO - Dropped 'volume' column from Finnifty_3.csv
2025-07-31 22:37:58,534 - feature_generator - INFO - Loaded 33305 rows from Finnifty_3.csv
2025-07-31 22:37:58,534 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:37:58,563 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:38:26,516 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:38:26,516 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:38:28,287 - feature_generator - INFO - Processed Finnifty_3.csv: 33106 rows, 53 features
2025-07-31 22:38:28,288 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_3.csv
2025-07-31 22:38:28,298 - feature_generator - INFO - Dropped 'volume' column from Finnifty_30.csv
2025-07-31 22:38:28,301 - feature_generator - INFO - Loaded 3466 rows from Finnifty_30.csv
2025-07-31 22:38:28,302 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:38:28,306 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:38:31,427 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:38:31,427 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:38:31,617 - feature_generator - INFO - Processed Finnifty_30.csv: 3267 rows, 53 features
2025-07-31 22:38:31,617 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_30.csv
2025-07-31 22:38:31,633 - feature_generator - INFO - Dropped 'volume' column from Finnifty_45.csv
2025-07-31 22:38:31,642 - feature_generator - INFO - Loaded 2399 rows from Finnifty_45.csv
2025-07-31 22:38:31,643 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:38:31,650 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:38:33,731 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:38:33,731 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:38:33,871 - feature_generator - INFO - Processed Finnifty_45.csv: 2200 rows, 53 features
2025-07-31 22:38:33,871 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_45.csv
2025-07-31 22:38:33,905 - feature_generator - INFO - Dropped 'volume' column from Finnifty_5.csv
2025-07-31 22:38:33,917 - feature_generator - INFO - Loaded 19983 rows from Finnifty_5.csv
2025-07-31 22:38:33,918 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:38:33,929 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:38:50,978 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:38:50,978 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:38:52,096 - feature_generator - INFO - Processed Finnifty_5.csv: 19784 rows, 53 features
2025-07-31 22:38:52,096 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_5.csv
2025-07-31 22:38:52,109 - feature_generator - INFO - Dropped 'volume' column from Finnifty_60.csv
2025-07-31 22:38:52,117 - feature_generator - INFO - Loaded 1867 rows from Finnifty_60.csv
2025-07-31 22:38:52,118 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:38:52,124 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:38:53,924 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:38:53,924 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:38:54,031 - feature_generator - INFO - Processed Finnifty_60.csv: 1668 rows, 53 features
2025-07-31 22:38:54,042 - feature_generator - INFO - Replaced existing file: data\final\features_Finnifty_60.csv
2025-07-31 22:38:54,151 - feature_generator - INFO - Dropped 'volume' column from Nifty_1.csv
2025-07-31 22:38:54,206 - feature_generator - INFO - Loaded 99915 rows from Nifty_1.csv
2025-07-31 22:38:54,207 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:38:54,247 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:40:17,422 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:40:17,422 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:40:22,512 - feature_generator - INFO - Processed Nifty_1.csv: 99716 rows, 53 features
2025-07-31 22:40:22,512 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_1.csv
2025-07-31 22:40:22,538 - feature_generator - INFO - Dropped 'volume' column from Nifty_10.csv
2025-07-31 22:40:22,544 - feature_generator - INFO - Loaded 10127 rows from Nifty_10.csv
2025-07-31 22:40:22,545 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:40:22,556 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:40:31,322 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:40:31,322 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:40:31,858 - feature_generator - INFO - Processed Nifty_10.csv: 9928 rows, 53 features
2025-07-31 22:40:31,859 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_10.csv
2025-07-31 22:40:31,859 - feature_generator - INFO - Dropped 'volume' column from Nifty_120.csv
2025-07-31 22:40:31,871 - feature_generator - INFO - Loaded 1067 rows from Nifty_120.csv
2025-07-31 22:40:31,871 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:40:31,873 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:40:32,886 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:40:32,886 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:40:32,947 - feature_generator - INFO - Processed Nifty_120.csv: 868 rows, 53 features
2025-07-31 22:40:32,947 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_120.csv
2025-07-31 22:40:32,969 - feature_generator - INFO - Dropped 'volume' column from Nifty_15.csv
2025-07-31 22:40:32,973 - feature_generator - INFO - Loaded 6661 rows from Nifty_15.csv
2025-07-31 22:40:32,974 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:40:32,980 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:40:38,882 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:40:38,882 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:40:39,238 - feature_generator - INFO - Processed Nifty_15.csv: 6462 rows, 53 features
2025-07-31 22:40:39,238 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_15.csv
2025-07-31 22:40:39,238 - feature_generator - INFO - Dropped 'volume' column from Nifty_180.csv
2025-07-31 22:40:39,260 - feature_generator - INFO - Loaded 802 rows from Nifty_180.csv
2025-07-31 22:40:39,263 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:40:39,267 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:40:40,007 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:40:40,007 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:40:40,057 - feature_generator - INFO - Processed Nifty_180.csv: 603 rows, 53 features
2025-07-31 22:40:40,057 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_180.csv
2025-07-31 22:40:40,127 - feature_generator - INFO - Dropped 'volume' column from Nifty_2.csv
2025-07-31 22:40:40,149 - feature_generator - INFO - Loaded 50093 rows from Nifty_2.csv
2025-07-31 22:40:40,149 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:40:40,173 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:41:21,928 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:41:21,928 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:41:24,610 - feature_generator - INFO - Processed Nifty_2.csv: 49894 rows, 53 features
2025-07-31 22:41:24,610 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_2.csv
2025-07-31 22:41:24,636 - feature_generator - INFO - Dropped 'volume' column from Nifty_20.csv
2025-07-31 22:41:24,643 - feature_generator - INFO - Loaded 5065 rows from Nifty_20.csv
2025-07-31 22:41:24,644 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:41:24,649 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:41:29,081 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:41:29,081 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:41:29,359 - feature_generator - INFO - Processed Nifty_20.csv: 4866 rows, 53 features
2025-07-31 22:41:29,359 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_20.csv
2025-07-31 22:41:29,375 - feature_generator - INFO - Dropped 'volume' column from Nifty_240.csv
2025-07-31 22:41:29,375 - feature_generator - INFO - Loaded 534 rows from Nifty_240.csv
2025-07-31 22:41:29,375 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:41:29,384 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:41:29,864 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:41:29,864 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:41:29,901 - feature_generator - INFO - Processed Nifty_240.csv: 335 rows, 53 features
2025-07-31 22:41:29,905 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_240.csv
2025-07-31 22:41:29,956 - feature_generator - INFO - Dropped 'volume' column from Nifty_3.csv
2025-07-31 22:41:29,978 - feature_generator - INFO - Loaded 33305 rows from Nifty_3.csv
2025-07-31 22:41:29,985 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:41:30,003 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:41:58,341 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:41:58,341 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:42:00,188 - feature_generator - INFO - Processed Nifty_3.csv: 33106 rows, 53 features
2025-07-31 22:42:00,203 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_3.csv
2025-07-31 22:42:00,203 - feature_generator - INFO - Dropped 'volume' column from Nifty_30.csv
2025-07-31 22:42:00,218 - feature_generator - INFO - Loaded 3466 rows from Nifty_30.csv
2025-07-31 22:42:00,218 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:42:00,227 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:42:03,241 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:42:03,241 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:42:03,441 - feature_generator - INFO - Processed Nifty_30.csv: 3267 rows, 53 features
2025-07-31 22:42:03,441 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_30.csv
2025-07-31 22:42:03,441 - feature_generator - INFO - Dropped 'volume' column from Nifty_45.csv
2025-07-31 22:42:03,452 - feature_generator - INFO - Loaded 2399 rows from Nifty_45.csv
2025-07-31 22:42:03,453 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:42:03,459 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:42:05,582 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:42:05,582 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:42:05,720 - feature_generator - INFO - Processed Nifty_45.csv: 2200 rows, 53 features
2025-07-31 22:42:05,720 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_45.csv
2025-07-31 22:42:05,754 - feature_generator - INFO - Dropped 'volume' column from Nifty_5.csv
2025-07-31 22:42:05,766 - feature_generator - INFO - Loaded 19983 rows from Nifty_5.csv
2025-07-31 22:42:05,766 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:42:05,779 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:42:22,313 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:42:22,313 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:42:23,384 - feature_generator - INFO - Processed Nifty_5.csv: 19784 rows, 53 features
2025-07-31 22:42:23,384 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_5.csv
2025-07-31 22:42:23,384 - feature_generator - INFO - Dropped 'volume' column from Nifty_60.csv
2025-07-31 22:42:23,397 - feature_generator - INFO - Loaded 1867 rows from Nifty_60.csv
2025-07-31 22:42:23,397 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:42:23,397 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:42:25,111 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:42:25,111 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:42:25,226 - feature_generator - INFO - Processed Nifty_60.csv: 1668 rows, 53 features
2025-07-31 22:42:25,226 - feature_generator - INFO - Replaced existing file: data\final\features_Nifty_60.csv
2025-07-31 22:42:25,346 - feature_generator - INFO - Dropped 'volume' column from Sensex_1.csv
2025-07-31 22:42:25,403 - feature_generator - INFO - Loaded 99915 rows from Sensex_1.csv
2025-07-31 22:42:25,403 - feature_generator - INFO - Processing in-memory DataFrame with 99915 rows...
2025-07-31 22:42:25,445 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:43:50,161 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 99716 rows
2025-07-31 22:43:50,161 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:43:55,496 - feature_generator - INFO - Processed Sensex_1.csv: 99716 rows, 53 features
2025-07-31 22:43:55,496 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_1.csv
2025-07-31 22:43:55,512 - feature_generator - INFO - Dropped 'volume' column from Sensex_10.csv
2025-07-31 22:43:55,535 - feature_generator - INFO - Loaded 10127 rows from Sensex_10.csv
2025-07-31 22:43:55,536 - feature_generator - INFO - Processing in-memory DataFrame with 10127 rows...
2025-07-31 22:43:55,545 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:44:04,261 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 9928 rows
2025-07-31 22:44:04,261 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:44:04,882 - feature_generator - INFO - Processed Sensex_10.csv: 9928 rows, 53 features
2025-07-31 22:44:04,882 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_10.csv
2025-07-31 22:44:04,882 - feature_generator - INFO - Dropped 'volume' column from Sensex_120.csv
2025-07-31 22:44:04,898 - feature_generator - INFO - Loaded 1067 rows from Sensex_120.csv
2025-07-31 22:44:04,898 - feature_generator - INFO - Processing in-memory DataFrame with 1067 rows...
2025-07-31 22:44:04,903 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:44:05,975 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 868 rows
2025-07-31 22:44:05,975 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:44:06,035 - feature_generator - INFO - Processed Sensex_120.csv: 868 rows, 53 features
2025-07-31 22:44:06,035 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_120.csv
2025-07-31 22:44:06,060 - feature_generator - INFO - Dropped 'volume' column from Sensex_15.csv
2025-07-31 22:44:06,063 - feature_generator - INFO - Loaded 6661 rows from Sensex_15.csv
2025-07-31 22:44:06,086 - feature_generator - INFO - Processing in-memory DataFrame with 6661 rows...
2025-07-31 22:44:06,105 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:44:11,894 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 6462 rows
2025-07-31 22:44:11,894 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:44:12,242 - feature_generator - INFO - Processed Sensex_15.csv: 6462 rows, 53 features
2025-07-31 22:44:12,242 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_15.csv
2025-07-31 22:44:12,242 - feature_generator - INFO - Dropped 'volume' column from Sensex_180.csv
2025-07-31 22:44:12,253 - feature_generator - INFO - Loaded 802 rows from Sensex_180.csv
2025-07-31 22:44:12,254 - feature_generator - INFO - Processing in-memory DataFrame with 802 rows...
2025-07-31 22:44:12,255 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:44:13,031 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 603 rows
2025-07-31 22:44:13,032 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:44:13,074 - feature_generator - INFO - Processed Sensex_180.csv: 603 rows, 53 features
2025-07-31 22:44:13,074 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_180.csv
2025-07-31 22:44:13,149 - feature_generator - INFO - Dropped 'volume' column from Sensex_2.csv
2025-07-31 22:44:13,179 - feature_generator - INFO - Loaded 50093 rows from Sensex_2.csv
2025-07-31 22:44:13,179 - feature_generator - INFO - Processing in-memory DataFrame with 50093 rows...
2025-07-31 22:44:13,199 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:44:55,061 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 49894 rows
2025-07-31 22:44:55,061 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:44:57,816 - feature_generator - INFO - Processed Sensex_2.csv: 49894 rows, 53 features
2025-07-31 22:44:57,816 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_2.csv
2025-07-31 22:44:57,832 - feature_generator - INFO - Dropped 'volume' column from Sensex_20.csv
2025-07-31 22:44:57,849 - feature_generator - INFO - Loaded 5065 rows from Sensex_20.csv
2025-07-31 22:44:57,849 - feature_generator - INFO - Processing in-memory DataFrame with 5065 rows...
2025-07-31 22:44:57,857 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:02,322 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 4866 rows
2025-07-31 22:45:02,322 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:02,593 - feature_generator - INFO - Processed Sensex_20.csv: 4866 rows, 53 features
2025-07-31 22:45:02,593 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_20.csv
2025-07-31 22:45:02,593 - feature_generator - INFO - Dropped 'volume' column from Sensex_240.csv
2025-07-31 22:45:02,608 - feature_generator - INFO - Loaded 534 rows from Sensex_240.csv
2025-07-31 22:45:02,609 - feature_generator - INFO - Processing in-memory DataFrame with 534 rows...
2025-07-31 22:45:02,610 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:03,137 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 335 rows
2025-07-31 22:45:03,137 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:03,177 - feature_generator - INFO - Processed Sensex_240.csv: 335 rows, 53 features
2025-07-31 22:45:03,179 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_240.csv
2025-07-31 22:45:03,234 - feature_generator - INFO - Dropped 'volume' column from Sensex_3.csv
2025-07-31 22:45:03,244 - feature_generator - INFO - Loaded 33305 rows from Sensex_3.csv
2025-07-31 22:45:03,244 - feature_generator - INFO - Processing in-memory DataFrame with 33305 rows...
2025-07-31 22:45:03,270 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:31,266 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 33106 rows
2025-07-31 22:45:31,266 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:33,125 - feature_generator - INFO - Processed Sensex_3.csv: 33106 rows, 53 features
2025-07-31 22:45:33,125 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_3.csv
2025-07-31 22:45:33,138 - feature_generator - INFO - Dropped 'volume' column from Sensex_30.csv
2025-07-31 22:45:33,147 - feature_generator - INFO - Loaded 3466 rows from Sensex_30.csv
2025-07-31 22:45:33,148 - feature_generator - INFO - Processing in-memory DataFrame with 3466 rows...
2025-07-31 22:45:33,153 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:36,233 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 3267 rows
2025-07-31 22:45:36,233 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:36,434 - feature_generator - INFO - Processed Sensex_30.csv: 3267 rows, 53 features
2025-07-31 22:45:36,434 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_30.csv
2025-07-31 22:45:36,434 - feature_generator - INFO - Dropped 'volume' column from Sensex_45.csv
2025-07-31 22:45:36,450 - feature_generator - INFO - Loaded 2399 rows from Sensex_45.csv
2025-07-31 22:45:36,450 - feature_generator - INFO - Processing in-memory DataFrame with 2399 rows...
2025-07-31 22:45:36,452 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:38,719 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 2200 rows
2025-07-31 22:45:38,719 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:38,860 - feature_generator - INFO - Processed Sensex_45.csv: 2200 rows, 53 features
2025-07-31 22:45:38,860 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_45.csv
2025-07-31 22:45:38,892 - feature_generator - INFO - Dropped 'volume' column from Sensex_5.csv
2025-07-31 22:45:38,904 - feature_generator - INFO - Loaded 19983 rows from Sensex_5.csv
2025-07-31 22:45:38,904 - feature_generator - INFO - Processing in-memory DataFrame with 19983 rows...
2025-07-31 22:45:38,919 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:55,865 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 19784 rows
2025-07-31 22:45:55,865 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:56,953 - feature_generator - INFO - Processed Sensex_5.csv: 19784 rows, 53 features
2025-07-31 22:45:56,953 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_5.csv
2025-07-31 22:45:56,966 - feature_generator - INFO - Dropped 'volume' column from Sensex_60.csv
2025-07-31 22:45:56,973 - feature_generator - INFO - Loaded 1867 rows from Sensex_60.csv
2025-07-31 22:45:56,974 - feature_generator - INFO - Processing in-memory DataFrame with 1867 rows...
2025-07-31 22:45:56,976 - feature_generator - INFO - ✅ Enhanced datetime processing: epoch as feature, readable as index
2025-07-31 22:45:58,953 - feature_generator - INFO - Removed 199 rows with NaN values. Final dataset: 1668 rows
2025-07-31 22:45:58,953 - feature_generator - INFO - Generated 48 features for DataFrame
2025-07-31 22:45:59,074 - feature_generator - INFO - Processed Sensex_60.csv: 1668 rows, 53 features
2025-07-31 22:45:59,074 - feature_generator - INFO - Replaced existing file: data\final\features_Sensex_60.csv
2025-07-31 22:45:59,074 - __main__ - INFO - Feature generator completed successfully
2025-07-31 22:45:59,074 - __main__ - INFO - Feature generation completed: 0 files
2025-07-31 22:45:59,074 - __main__ - INFO - Pipeline report saved: reports\pipeline\pipeline_report_1753982159.txt
2025-07-31 22:45:59,074 - __main__ - INFO - ================================================================================
2025-07-31 22:45:59,074 - __main__ - INFO - PIPELINE COMPLETED SUCCESSFULLY
2025-07-31 22:45:59,074 - __main__ - INFO - ================================================================================

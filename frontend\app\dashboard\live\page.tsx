"use client"
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */

import * as React from "react"
import { motion } from "framer-motion"
import { 
  Play, 
  Square, 
  TrendingUp, 
  Activity,
  Clock,
  DollarSign,
  Target,
  AlertCircle,
  Wifi,
  WifiOff
} from "lucide-react"

import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { TradingViewLayout } from "@/components/trading-view-layout"
import { TradingChart, createTradeMarker } from "@/components/trading-chart"
import { apiClient, formatApiError } from "@/lib/api"
import { useLiveTrading } from "@/hooks/use-websocket"
import { generateDemoData, generateDemoTradeMarkers, generateDemoPortfolioData, getRandomDemoDataset } from "@/lib/demo-data"

// Mock instruments data
const instruments = [
  { symbol: "Bank_Nifty", type: "index", name: "Bank Nifty" },
  { symbol: "Nifty", type: "index", name: "Nifty 50" },
  { symbol: "RELIANCE", type: "stock", name: "Reliance Industries" },
  { symbol: "TCS", type: "stock", name: "Tata Consultancy Services" },
  { symbol: "HDFC", type: "stock", name: "HDFC Bank" }
]

const timeframes = [
  { value: "1", label: "1 Minute" },
  { value: "5", label: "5 Minutes" },
  { value: "15", label: "15 Minutes" },
  { value: "30", label: "30 Minutes" },
  { value: "60", label: "1 Hour" }
]

const optionStrategies = [
  { value: "ITM", label: "In The Money (ITM)" },
  { value: "ATM", label: "At The Money (ATM)" },
  { value: "OTM", label: "Out of The Money (OTM)" }
]

export default function LiveTradePage() {
  const [userId] = React.useState("user123") // TODO: Get from auth context
  const [error, setError] = React.useState<string | null>(null)
  const [formData, setFormData] = React.useState({
    instrument: "",
    timeframe: "",
    optionStrategy: "ITM"
  })

  // Demo data state
  const [demoData, setDemoData] = React.useState<any[]>([])
  const [demoTradeMarkers, setDemoTradeMarkers] = React.useState<any[]>([])
  const [showDemo, setShowDemo] = React.useState(true)

  // Initialize demo data on component mount
  React.useEffect(() => {
    const demoDataset = getRandomDemoDataset()
    const candleData = demoDataset.data
    const tradeMarkers = generateDemoTradeMarkers(candleData, 0.05)

    setDemoData(candleData)
    setDemoTradeMarkers(tradeMarkers)
  }, [])

  // Use WebSocket hook for real-time live trading data
  const {
    isConnected,
    isTrading,
    stats,
    trades,
    error: wsError
  } = useLiveTrading(userId)

  const selectedInstrument = instruments.find(i => i.symbol === formData.instrument)
  const isIndexInstrument = selectedInstrument?.type === "index"

  const handleStart = async () => {
    try {
      setError(null)

      // Start live trading using API client
      const response = await apiClient.startLiveTrading({
        instrument: formData.instrument,
        timeframe: formData.timeframe,
        option_strategy: isIndexInstrument ? formData.optionStrategy : undefined
      })

      if (response.status !== "started") {
        setError(response.message || "Failed to start live trading")
      }
    } catch (err) {
      setError(formatApiError(err))
    }
  }

  const handleStop = async () => {
    try {
      setError(null)

      // Stop live trading using API client
      const response = await apiClient.stopLiveTrading()

      if (response.status !== "stopped") {
        setError(response.message || "Failed to stop live trading")
      }
    } catch (err) {
      setError(formatApiError(err))
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const isFormValid = formData.instrument && formData.timeframe

  // Create header controls for TradingView layout
  const headerControls = (
    <div className="flex items-center gap-3 text-xs overflow-x-auto scrollbar-hide min-w-0 flex-1">
      <div className="flex items-center gap-1">
        <label className="text-muted-foreground font-medium">Symbol:</label>
        <Select
          value={formData.instrument}
          onValueChange={(value) => setFormData(prev => ({ ...prev, instrument: value }))}
        >
          <SelectTrigger className="w-32 h-8">
            <SelectValue placeholder="Symbol" />
          </SelectTrigger>
          <SelectContent>
            {instruments.map((instrument) => (
              <SelectItem key={instrument.symbol} value={instrument.symbol}>
                {instrument.symbol}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center gap-1">
        <label className="text-muted-foreground font-medium">Timeframe:</label>
        <Select
          value={formData.timeframe}
          onValueChange={(value) => setFormData(prev => ({ ...prev, timeframe: value }))}
        >
          <SelectTrigger className="w-24 h-8">
            <SelectValue placeholder="TF" />
          </SelectTrigger>
          <SelectContent>
            {timeframes.map((tf) => (
              <SelectItem key={tf.value} value={tf.value}>
                {tf.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {isIndexInstrument && (
        <div className="flex items-center gap-1">
          <label className="text-muted-foreground font-medium">Strategy:</label>
          <Select
            value={formData.optionStrategy}
            onValueChange={(value) => setFormData(prev => ({ ...prev, optionStrategy: value }))}
          >
            <SelectTrigger className="w-20 h-8">
              <SelectValue placeholder="Strategy" />
            </SelectTrigger>
            <SelectContent>
              {optionStrategies.map((strategy) => (
                <SelectItem key={strategy.value} value={strategy.value}>
                  {strategy.value}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="flex items-center gap-1">
        {isConnected ? (
          <Wifi className="h-3 w-3 text-green-500" />
        ) : (
          <WifiOff className="h-3 w-3 text-red-500" />
        )}
        <span className="text-xs text-muted-foreground">
          {isConnected ? 'Connected' : 'Disconnected'}
        </span>
      </div>

      {!isTrading ? (
        <Button
          onClick={handleStart}
          disabled={!isFormValid || !isConnected}
          size="sm"
          className="h-8 px-3"
        >
          <Play className="h-3 w-3 mr-1" />
          Start
        </Button>
      ) : (
        <Button
          onClick={handleStop}
          variant="destructive"
          size="sm"
          className="h-8 px-3"
        >
          <Square className="h-3 w-3 mr-1" />
          Stop
        </Button>
      )}
    </div>
  )

  return (
    <TradingViewLayout headerControls={headerControls}>
      {/* Full-screen chart */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="h-full w-full"
      >
        {showDemo && !isTrading ? (
          // Show demo chart initially
          <TradingChart
            candlestickData={demoData}
            tradeMarkers={demoTradeMarkers}
            title="Demo Live Trading Chart"
            showVolume={true}
            showPortfolio={false}
            fullScreen={true}
            windowSize={100}
            enableSlidingWindow={false}
            currentPrice={demoData.length > 0 ? demoData[demoData.length - 1]?.close : undefined}
          />
        ) : isTrading && trades.length > 0 ? (
          // Show real live trading data based on trades
          <TradingChart
            candlestickData={trades.map((trade: any) => ({
              time: trade.timestamp,
              open: trade.price,
              high: trade.price * 1.001,
              low: trade.price * 0.999,
              close: trade.price,
              volume: 1000
            }))}
            tradeMarkers={trades.map((trade: any) =>
              createTradeMarker(
                trade.timestamp,
                trade.action as 'BUY' | 'SELL' | 'CLOSE_LONG' | 'CLOSE_SHORT' | 'HOLD',
                trade.price
              )
            )}
            title="Live Trading Chart"
            showVolume={true}
            showPortfolio={false}
            fullScreen={true}
            windowSize={100}
            enableSlidingWindow={false}
            currentPrice={stats.currentPrice}
          />
        ) : (
          // Empty state
          <div className="h-full w-full flex items-center justify-center bg-muted/5">
            <div className="text-center">
              <TrendingUp className="h-16 w-16 mx-auto mb-4 text-muted-foreground" />
              <h3 className="text-lg font-medium mb-2">Ready for Live Trading</h3>
              <p className="text-muted-foreground">
                Configure your parameters in the header and click Start to begin
              </p>
              {!isConnected && (
                <p className="text-red-500 text-sm mt-2">
                  Waiting for connection...
                </p>
              )}
            </div>
          </div>
        )}
      </motion.div>
    </TradingViewLayout>
  )
}
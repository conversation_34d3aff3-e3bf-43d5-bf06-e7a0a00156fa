{"cells": [{"cell_type": "code", "execution_count": 9, "id": "5fb81d08", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>datetime_readable</th>\n", "      <th>open</th>\n", "      <th>high</th>\n", "      <th>low</th>\n", "      <th>close</th>\n", "      <th>datetime_epoch</th>\n", "      <th>sma_5</th>\n", "      <th>ema_5</th>\n", "      <th>sma_10</th>\n", "      <th>ema_10</th>\n", "      <th>...</th>\n", "      <th>upper_shadow</th>\n", "      <th>lower_shadow</th>\n", "      <th>gap_up</th>\n", "      <th>gap_down</th>\n", "      <th>sma_5_20_cross</th>\n", "      <th>sma_10_50_cross</th>\n", "      <th>price_vs_sma_20</th>\n", "      <th>price_vs_ema_20</th>\n", "      <th>volatility_10</th>\n", "      <th>volatility_20</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2024-03-20 13:20:00</td>\n", "      <td>46535.90</td>\n", "      <td>46655.55</td>\n", "      <td>46524.55</td>\n", "      <td>46632.60</td>\n", "      <td>1710921000</td>\n", "      <td>46513.23</td>\n", "      <td>46537.13</td>\n", "      <td>46479.48</td>\n", "      <td>46484.21</td>\n", "      <td>...</td>\n", "      <td>0.05</td>\n", "      <td>0.02</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.50</td>\n", "      <td>0.48</td>\n", "      <td>0.15</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2024-03-20 13:25:00</td>\n", "      <td>46635.30</td>\n", "      <td>46649.10</td>\n", "      <td>46581.25</td>\n", "      <td>46588.65</td>\n", "      <td>1710921300</td>\n", "      <td>46548.70</td>\n", "      <td>46554.31</td>\n", "      <td>46495.05</td>\n", "      <td>46503.20</td>\n", "      <td>...</td>\n", "      <td>0.03</td>\n", "      <td>0.02</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.37</td>\n", "      <td>0.35</td>\n", "      <td>0.16</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2024-03-20 13:30:00</td>\n", "      <td>46593.65</td>\n", "      <td>46593.65</td>\n", "      <td>46516.50</td>\n", "      <td>46528.55</td>\n", "      <td>1710921600</td>\n", "      <td>46564.60</td>\n", "      <td>46545.72</td>\n", "      <td>46501.72</td>\n", "      <td>46507.81</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.21</td>\n", "      <td>0.20</td>\n", "      <td>0.16</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2024-03-20 13:35:00</td>\n", "      <td>46532.80</td>\n", "      <td>46568.10</td>\n", "      <td>46518.55</td>\n", "      <td>46561.70</td>\n", "      <td>1710921900</td>\n", "      <td>46568.41</td>\n", "      <td>46551.05</td>\n", "      <td>46511.99</td>\n", "      <td>46517.61</td>\n", "      <td>...</td>\n", "      <td>0.01</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.25</td>\n", "      <td>0.24</td>\n", "      <td>0.16</td>\n", "      <td>0.22</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2024-03-20 13:40:00</td>\n", "      <td>46562.25</td>\n", "      <td>46594.70</td>\n", "      <td>46541.15</td>\n", "      <td>46591.15</td>\n", "      <td>1710922200</td>\n", "      <td>46580.53</td>\n", "      <td>46564.41</td>\n", "      <td>46523.66</td>\n", "      <td>46530.98</td>\n", "      <td>...</td>\n", "      <td>0.01</td>\n", "      <td>0.05</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.28</td>\n", "      <td>0.28</td>\n", "      <td>0.17</td>\n", "      <td>0.21</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19779</th>\n", "      <td>2025-04-17 15:05:00</td>\n", "      <td>54334.25</td>\n", "      <td>54336.85</td>\n", "      <td>54260.40</td>\n", "      <td>54269.75</td>\n", "      <td>1744882500</td>\n", "      <td>54318.18</td>\n", "      <td>54310.99</td>\n", "      <td>54327.16</td>\n", "      <td>54318.62</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.02</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>-0.11</td>\n", "      <td>-0.03</td>\n", "      <td>0.07</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19780</th>\n", "      <td>2025-04-17 15:10:00</td>\n", "      <td>54263.40</td>\n", "      <td>54282.00</td>\n", "      <td>54252.40</td>\n", "      <td>54259.20</td>\n", "      <td>1744882800</td>\n", "      <td>54298.65</td>\n", "      <td>54293.73</td>\n", "      <td>54324.66</td>\n", "      <td>54307.82</td>\n", "      <td>...</td>\n", "      <td>0.03</td>\n", "      <td>0.01</td>\n", "      <td>0.00</td>\n", "      <td>0.01</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>-0.13</td>\n", "      <td>-0.04</td>\n", "      <td>0.08</td>\n", "      <td>0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19781</th>\n", "      <td>2025-04-17 15:15:00</td>\n", "      <td>54260.10</td>\n", "      <td>54332.25</td>\n", "      <td>54246.10</td>\n", "      <td>54307.05</td>\n", "      <td>1744883100</td>\n", "      <td>54296.84</td>\n", "      <td>54298.17</td>\n", "      <td>54326.96</td>\n", "      <td>54307.68</td>\n", "      <td>...</td>\n", "      <td>0.05</td>\n", "      <td>0.03</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>-0.03</td>\n", "      <td>0.04</td>\n", "      <td>0.08</td>\n", "      <td>0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19782</th>\n", "      <td>2025-04-17 15:20:00</td>\n", "      <td>54308.55</td>\n", "      <td>54308.65</td>\n", "      <td>54283.75</td>\n", "      <td>54296.05</td>\n", "      <td>1744883400</td>\n", "      <td>54292.76</td>\n", "      <td>54297.46</td>\n", "      <td>54319.18</td>\n", "      <td>54305.56</td>\n", "      <td>...</td>\n", "      <td>0.00</td>\n", "      <td>0.02</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>-0.05</td>\n", "      <td>0.02</td>\n", "      <td>0.07</td>\n", "      <td>0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19783</th>\n", "      <td>2025-04-17 15:25:00</td>\n", "      <td>54297.85</td>\n", "      <td>54306.95</td>\n", "      <td>54214.60</td>\n", "      <td>54252.05</td>\n", "      <td>1744883700</td>\n", "      <td>54276.82</td>\n", "      <td>54282.32</td>\n", "      <td>54306.40</td>\n", "      <td>54295.83</td>\n", "      <td>...</td>\n", "      <td>0.02</td>\n", "      <td>0.07</td>\n", "      <td>0.00</td>\n", "      <td>0.00</td>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>-0.12</td>\n", "      <td>-0.06</td>\n", "      <td>0.07</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>19784 rows × 64 columns</p>\n", "</div>"], "text/plain": ["         datetime_readable      open      high       low     close  \\\n", "0      2024-03-20 13:20:00  46535.90  46655.55  46524.55  46632.60   \n", "1      2024-03-20 13:25:00  46635.30  46649.10  46581.25  46588.65   \n", "2      2024-03-20 13:30:00  46593.65  46593.65  46516.50  46528.55   \n", "3      2024-03-20 13:35:00  46532.80  46568.10  46518.55  46561.70   \n", "4      2024-03-20 13:40:00  46562.25  46594.70  46541.15  46591.15   \n", "...                    ...       ...       ...       ...       ...   \n", "19779  2025-04-17 15:05:00  54334.25  54336.85  54260.40  54269.75   \n", "19780  2025-04-17 15:10:00  54263.40  54282.00  54252.40  54259.20   \n", "19781  2025-04-17 15:15:00  54260.10  54332.25  54246.10  54307.05   \n", "19782  2025-04-17 15:20:00  54308.55  54308.65  54283.75  54296.05   \n", "19783  2025-04-17 15:25:00  54297.85  54306.95  54214.60  54252.05   \n", "\n", "       datetime_epoch     sma_5     ema_5    sma_10    ema_10  ...  \\\n", "0          1710921000  46513.23  46537.13  46479.48  46484.21  ...   \n", "1          1710921300  46548.70  46554.31  46495.05  46503.20  ...   \n", "2          1710921600  46564.60  46545.72  46501.72  46507.81  ...   \n", "3          1710921900  46568.41  46551.05  46511.99  46517.61  ...   \n", "4          1710922200  46580.53  46564.41  46523.66  46530.98  ...   \n", "...               ...       ...       ...       ...       ...  ...   \n", "19779      1744882500  54318.18  54310.99  54327.16  54318.62  ...   \n", "19780      1744882800  54298.65  54293.73  54324.66  54307.82  ...   \n", "19781      1744883100  54296.84  54298.17  54326.96  54307.68  ...   \n", "19782      1744883400  54292.76  54297.46  54319.18  54305.56  ...   \n", "19783      1744883700  54276.82  54282.32  54306.40  54295.83  ...   \n", "\n", "       upper_shadow  lower_shadow  gap_up  gap_down  sma_5_20_cross  \\\n", "0              0.05          0.02    0.01      0.00               1   \n", "1              0.03          0.02    0.01      0.00               1   \n", "2              0.00          0.03    0.01      0.00               1   \n", "3              0.01          0.03    0.01      0.00               1   \n", "4              0.01          0.05    0.00      0.00               1   \n", "...             ...           ...     ...       ...             ...   \n", "19779          0.00          0.02    0.00      0.00              -1   \n", "19780          0.03          0.01    0.00      0.01              -1   \n", "19781          0.05          0.03    0.00      0.00              -1   \n", "19782          0.00          0.02    0.00      0.00              -1   \n", "19783          0.02          0.07    0.00      0.00              -1   \n", "\n", "       sma_10_50_cross  price_vs_sma_20  price_vs_ema_20  volatility_10  \\\n", "0                    1             0.50             0.48           0.15   \n", "1                    1             0.37             0.35           0.16   \n", "2                    1             0.21             0.20           0.16   \n", "3                    1             0.25             0.24           0.16   \n", "4                    1             0.28             0.28           0.17   \n", "...                ...              ...              ...            ...   \n", "19779                1            -0.11            -0.03           0.07   \n", "19780                1            -0.13            -0.04           0.08   \n", "19781                1            -0.03             0.04           0.08   \n", "19782                1            -0.05             0.02           0.07   \n", "19783                1            -0.12            -0.06           0.07   \n", "\n", "       volatility_20  \n", "0               0.22  \n", "1               0.22  \n", "2               0.22  \n", "3               0.22  \n", "4               0.21  \n", "...              ...  \n", "19779           0.08  \n", "19780           0.07  \n", "19781           0.07  \n", "19782           0.07  \n", "19783           0.08  \n", "\n", "[19784 rows x 64 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "pd.read_csv(\"data/final/features_Bank_Nifty_5.csv\")"]}, {"cell_type": "code", "execution_count": null, "id": "7f36d99b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.0"}}, "nbformat": 4, "nbformat_minor": 5}
# Training Sequence Configuration
# PPO Training Only - Simplified for single-stage training
# PRODUCTION VALUES - Optimized for robust real-world trading performance

training_sequence:
  # Stage 1: PPO Training
  stage_1_ppo:
    algorithm: "PPO"
    episodes: 5  # PRODUCTION: Sufficient episodes for stable learning
    description: "PPO reinforcement learning for trading"
    objectives:
      - "Learn optimal trading patterns"
      - "Maximize risk-adjusted returns"
      - "Develop robust trading strategies"
    success_criteria:
      min_win_rate: 0.35  # Realistic for options trading
      min_profit_factor: 0.8
      max_drawdown: 0.4

# Environment Configuration - Centralized settings
environment:
  initial_capital: 100000.0
  lookback_window: 50  # Increased from 20 to 50 for more historical context
  episode_length: 500
  use_streaming: false
  reward_function: "enhanced_trading_focused"  # New enhanced reward function
  trailing_stop_percentage: 0.015  # Tighter trailing stop for better risk management
  smart_action_filtering: true  # Prevent redundant position attempts

# Model Configuration - Moved from src/config/config.py
model:
  hidden_dim: 256          # Hidden layer dimension for PPO agent
  action_dim_discrete: 5  # Number of discrete actions (BUY_LONG, SELL_SHORT, CLOSE_LONG, CL<PERSON>E_SHORT, HOLD)
  action_dim_continuous: 1 # Continuous action dimension (quantity: 1 to max_affordable)
  model_path: "models/universal_final_model.pth"
  model_type: "ppo"

# Risk Management Configuration - Moved from src/config/config.py
risk_management:
  risk_multiplier: 1.5    # SL = 1.5 * ATR (tighter stops)
  reward_multiplier: 3.0  # TP = 3 * ATR (1:2 Risk-Reward Ratio)
  use_atr_based_stops: true
  max_risk_per_trade: 0.015  # Maximum 1.5% risk per trade (tighter)
  stop_loss_percentage: 0.015  # Tighter stop loss
  target_profit_percentage: 0.045  # Higher profit target (3:1 risk-reward)
  win_rate_bonus_threshold: 0.6  # Bonus for high win rate
  profit_factor_bonus_threshold: 1.5  # Bonus for good profit factor

# Trading Configuration
trading:
  brokerage_entry: 25  # Entry brokerage in INR
  brokerage_exit: 35   # Exit brokerage in INR

# Training Parameters - Optimized for Production Performance
training_params:
  # PPO specific parameters
  ppo:
    learning_rate: 0.0001  # PRODUCTION: Lower LR for stable, robust learning
    batch_size: 128       # PRODUCTION: Larger batch for better gradient estimates
    gamma: 0.995          # PRODUCTION: Higher gamma for longer-term planning in trading
    gae_lambda: 0.95      # Standard GAE lambda for advantage estimation
    clip_epsilon: 0.15    # PRODUCTION: Conservative clipping for stable policy updates
    k_epochs: 6           # PRODUCTION: More epochs for thorough policy optimization

# Validation and Progression Rules - Production Standards
progression_rules:
  # No automatic progression needed for single-stage training
  auto_progression: false

  # Training completion criteria
  completion_criteria:
    min_episodes: 300  # PRODUCTION: Ensure sufficient learning
    min_win_rate: 0.35  # PRODUCTION: Realistic performance threshold
    min_profit_factor: 0.8  # PRODUCTION: Minimum profitability requirement

  # Fallback strategies if criteria not met
  fallback_strategies:
    extend_training: true
    max_extension_episodes: 200  # PRODUCTION: More training if needed
    hyperparameter_tuning: true

# Output and Monitoring
monitoring:
  save_checkpoints: true
  checkpoint_frequency: 50  # episodes
  generate_reports: true
  compare_stages: true
  
# Model Management
model_management:
  save_stage_models: true
  model_naming_convention: "{symbol}_{stage}_{algorithm}_{timestamp}"
  keep_best_models: true
  transfer_learning: true  # Use previous stage as initialization

# Testing Configuration - Minimal values for quick pipeline validation
testing_overrides:
  # Override training sequence for testing
  training_sequence:
    stage_1_ppo:
      episodes: 5  # Minimal episodes for testing

  # Override training parameters for testing
  training_params:
    ppo:
      learning_rate: 0.001  # Higher LR for faster testing
      batch_size: 32  # Smaller batch for testing
      gamma: 0.99  # Standard gamma for testing
      k_epochs: 3  # Fewer epochs for testing

  # Override progression rules for testing
  progression_rules:
    completion_criteria:
      min_episodes: 3  # Very low threshold for testing
      min_win_rate: 0.10  # Very relaxed for testing
      min_profit_factor: 0.1  # Very relaxed for testing
    fallback_strategies:
      max_extension_episodes: 20  # Minimal extension for testing

  # Testing data configuration
  test_data:
    num_rows: 500  # Sufficient data for technical indicators + NaN removal
    symbols: ["RELIANCE_1", "Bank_Nifty_5"]  # Test with different data sources (with timeframes)
    save_models: false  # Don't save models in testing mode

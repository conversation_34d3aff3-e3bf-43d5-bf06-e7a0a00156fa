{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:05:54,219+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:06:15,006+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-30 18:06:15,821+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:07:41,996+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:07:42,891+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:08:14,092+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:09:30,966+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:09:32,014+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:10:11,071+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:10:11,943+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:15:43,397+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-30 18:15:44,283+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-07-30 18:15:45,285+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:15:46,330+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:18:13,085+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:18:14,003+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:19:26,532+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:19:30,513+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:29:52,603+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:29:56,797+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 18:59:52,073+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 18:59:53,187+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 19:01:32,951+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 19:01:34,093+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 19:02:28,087+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 19:02:29,543+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 20:20:27,082+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 20:20:29,167+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 20:41:56,872+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 20:50:04,683+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 20:50:09,062+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:00:28,014+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:00:30,294+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:05:03,171+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:05:09,403+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:20:40,795+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:20:42,049+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:32:37,061+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:32:38,289+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:47:08,830+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:47:10,165+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:50:29,753+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:50:33,286+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 21:59:52,836+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 21:59:54,840+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 22:11:39,602+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 22:11:40,896+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 22:17:10,183+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 22:17:11,882+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 22:25:06,077+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 22:25:08,129+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 22:27:26,562+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 22:27:32,280+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 22:52:30,283+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 22:52:33,359+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-30 22:55:05,362+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-30 22:55:06,903+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 11:38:13,662+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-31 11:38:14,977+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 11:42:28,776+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-31 11:42:29,901+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 12:06:01,262+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-31 12:06:02,398+0530","service":"FyersAPIRequest"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 16:49:41,730+0530","service":"FyersAPIRequest","taskName":"Task-16"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 16:49:43,510+0530","service":"FyersAPIRequest","taskName":"Task-16"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 16:49:44,736+0530","service":"FyersAPIRequest","taskName":"Task-17"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 16:49:45,838+0530","service":"FyersAPIRequest","taskName":"Task-17"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 17:02:55,682+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 17:02:56,976+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 17:02:58,219+0530","service":"FyersAPIRequest","taskName":"Task-9"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 17:02:59,381+0530","service":"FyersAPIRequest","taskName":"Task-9"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 18:29:13,622+0530","service":"FyersAPIRequest","taskName":"Task-29"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 18:29:14,669+0530","service":"FyersAPIRequest","taskName":"Task-29"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 18:29:16,419+0530","service":"FyersAPIRequest","taskName":"Task-30"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 18:29:19,076+0530","service":"FyersAPIRequest","taskName":"Task-30"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 18:41:17,876+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 18:41:20,145+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 18:41:21,269+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 18:41:22,490+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-31 18:42:47,910+0530","service":"FyersAPIRequest","taskName":"Task-20"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 20:53:00,378+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 20:53:01,859+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 20:53:05,045+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 20:53:07,014+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:30:40,600+0530","service":"FyersAPIRequest","taskName":"Task-57"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:30:41,521+0530","service":"FyersAPIRequest","taskName":"Task-57"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:30:57,600+0530","service":"FyersAPIRequest","taskName":"Task-70"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:30:59,340+0530","service":"FyersAPIRequest","taskName":"Task-70"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:31:02,268+0530","service":"FyersAPIRequest","taskName":"Task-76"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:31:03,601+0530","service":"FyersAPIRequest","taskName":"Task-76"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:09,711+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:10,574+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:12,188+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:13,067+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:14,041+0530","service":"FyersAPIRequest","taskName":"Task-24"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:14,907+0530","service":"FyersAPIRequest","taskName":"Task-24"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:15,187+0530","service":"FyersAPIRequest","taskName":"Task-22"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:15,477+0530","service":"FyersAPIRequest","taskName":"Task-30"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:21,275+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:22,273+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:24,707+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:25,535+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:25,776+0530","service":"FyersAPIRequest","taskName":"Task-40"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:29,467+0530","service":"FyersAPIRequest","taskName":"Task-45"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:30,351+0530","service":"FyersAPIRequest","taskName":"Task-45"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:42:31,855+0530","service":"FyersAPIRequest","taskName":"Task-46"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:34,148+0530","service":"FyersAPIRequest","taskName":"Task-46"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:42:36,095+0530","service":"FyersAPIRequest","taskName":"Task-49"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:48:02,109+0530","service":"FyersAPIRequest","taskName":"Task-54"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:48:03,059+0530","service":"FyersAPIRequest","taskName":"Task-54"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:48:06,148+0530","service":"FyersAPIRequest","taskName":"Task-59"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:48:07,268+0530","service":"FyersAPIRequest","taskName":"Task-59"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:48:07,616+0530","service":"FyersAPIRequest","taskName":"Task-58"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:53:07,001+0530","service":"FyersAPIRequest","taskName":"Task-65"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:53:14,982+0530","service":"FyersAPIRequest","taskName":"Task-65"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:53:17,948+0530","service":"FyersAPIRequest","taskName":"Task-78"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:53:18,858+0530","service":"FyersAPIRequest","taskName":"Task-78"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:53:19,379+0530","service":"FyersAPIRequest","taskName":"Task-76"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-07-31 21:54:14,391+0530","service":"FyersAPIRequest","taskName":"Task-83"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-07-31 21:54:16,307+0530","service":"FyersAPIRequest","taskName":"Task-83"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-07-31 21:54:48,690+0530","service":"FyersAPIRequest","taskName":"Task-89"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:10:03,826+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:04,754+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:10:16,938+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:17,848+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:10:20,941+0530","service":"FyersAPIRequest","taskName":"Task-15"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:21,743+0530","service":"FyersAPIRequest","taskName":"Task-15"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:10:22,675+0530","service":"FyersAPIRequest","taskName":"Task-29"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:23,507+0530","service":"FyersAPIRequest","taskName":"Task-29"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:23,906+0530","service":"FyersAPIRequest","taskName":"Task-22"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:24,288+0530","service":"FyersAPIRequest","taskName":"Task-31"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:10:34,082+0530","service":"FyersAPIRequest","taskName":"Task-35"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:10:35,691+0530","service":"FyersAPIRequest","taskName":"Task-35"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:21:44,542+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:21:45,622+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:35:26,858+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:35:31,322+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:35:58,701+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:35:59,531+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:36:13,598+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:36:14,841+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:36:29,594+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:36:33,394+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:37:29,526+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:37:30,361+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:40:30,134+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:40:31,747+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:41:10,437+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:41:11,764+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:45:13,470+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:45:15,387+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:56:57,348+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:56:58,226+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:56:59,865+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:00,865+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:57:01,747+0530","service":"FyersAPIRequest","taskName":"Task-27"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:02,736+0530","service":"FyersAPIRequest","taskName":"Task-27"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:02,893+0530","service":"FyersAPIRequest","taskName":"Task-15"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:03,152+0530","service":"FyersAPIRequest","taskName":"Task-30"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:57:09,815+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:11,282+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:57:13,667+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:14,454+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:14,654+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:57:26,981+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:57:28,377+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:59:37,630+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:59:38,462+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:59:41,066+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:59:45,086+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 00:59:46,069+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 00:59:46,969+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:00:13,498+0530","service":"FyersAPIRequest","taskName":"Task-9"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:00:16,047+0530","service":"FyersAPIRequest","taskName":"Task-9"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:00:18,898+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:00:19,948+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:00:34,671+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:00:36,557+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:00:40,103+0530","service":"FyersAPIRequest","taskName":"Task-15"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:00:40,933+0530","service":"FyersAPIRequest","taskName":"Task-15"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:00:50,776+0530","service":"FyersAPIRequest","taskName":"Task-17"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:00:52,435+0530","service":"FyersAPIRequest","taskName":"Task-17"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:00:55,977+0530","service":"FyersAPIRequest","taskName":"Task-19"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:00:57,369+0530","service":"FyersAPIRequest","taskName":"Task-19"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:03:24,753+0530","service":"FyersAPIRequest","taskName":"Task-21"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:03:25,801+0530","service":"FyersAPIRequest","taskName":"Task-21"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:03:28,578+0530","service":"FyersAPIRequest","taskName":"Task-24"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:03:30,475+0530","service":"FyersAPIRequest","taskName":"Task-24"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:03:32,714+0530","service":"FyersAPIRequest","taskName":"Task-25"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:03:34,090+0530","service":"FyersAPIRequest","taskName":"Task-25"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 01:03:51,053+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:06:09,213+0530","service":"FyersAPIRequest","taskName":"Task-1291"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:06:11,046+0530","service":"FyersAPIRequest","taskName":"Task-1291"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:22:50,051+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:22:51,346+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:22:53,253+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:22:54,047+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:23:26,172+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:23:27,021+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:24:20,329+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:24:21,291+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:24:23,411+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:24:24,538+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:24:34,168+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:24:35,007+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:25:02,011+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:25:02,789+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:26:55,446+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:26:56,552+0530","service":"FyersAPIRequest","taskName":"Task-14"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:27:02,804+0530","service":"FyersAPIRequest","taskName":"Task-16"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:27:03,969+0530","service":"FyersAPIRequest","taskName":"Task-16"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:27:06,206+0530","service":"FyersAPIRequest","taskName":"Task-18"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:27:07,020+0530","service":"FyersAPIRequest","taskName":"Task-18"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:29:17,476+0530","service":"FyersAPIRequest","taskName":"Task-20"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:29:18,336+0530","service":"FyersAPIRequest","taskName":"Task-20"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:29:20,561+0530","service":"FyersAPIRequest","taskName":"Task-22"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:29:21,916+0530","service":"FyersAPIRequest","taskName":"Task-22"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:29:59,969+0530","service":"FyersAPIRequest","taskName":"Task-24"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:30:02,092+0530","service":"FyersAPIRequest","taskName":"Task-24"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:30:03,169+0530","service":"FyersAPIRequest","taskName":"Task-26"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:30:04,446+0530","service":"FyersAPIRequest","taskName":"Task-26"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:30:38,684+0530","service":"FyersAPIRequest","taskName":"Task-28"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:30:39,773+0530","service":"FyersAPIRequest","taskName":"Task-28"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:30:42,598+0530","service":"FyersAPIRequest","taskName":"Task-30"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:30:43,719+0530","service":"FyersAPIRequest","taskName":"Task-30"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:31:01,690+0530","service":"FyersAPIRequest","taskName":"Task-32"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:31:04,152+0530","service":"FyersAPIRequest","taskName":"Task-32"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:31:05,566+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:31:07,755+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:31:30,576+0530","service":"FyersAPIRequest","taskName":"Task-36"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:31:33,041+0530","service":"FyersAPIRequest","taskName":"Task-36"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:31:34,353+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:31:35,643+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:32:16,643+0530","service":"FyersAPIRequest","taskName":"Task-40"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:32:17,446+0530","service":"FyersAPIRequest","taskName":"Task-40"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:32:28,713+0530","service":"FyersAPIRequest","taskName":"Task-42"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:32:29,507+0530","service":"FyersAPIRequest","taskName":"Task-42"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:32:31,046+0530","service":"FyersAPIRequest","taskName":"Task-44"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:32:34,056+0530","service":"FyersAPIRequest","taskName":"Task-44"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:34:39,465+0530","service":"FyersAPIRequest","taskName":"Task-46"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:34:40,715+0530","service":"FyersAPIRequest","taskName":"Task-46"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:34:42,278+0530","service":"FyersAPIRequest","taskName":"Task-48"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:34:45,342+0530","service":"FyersAPIRequest","taskName":"Task-48"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:34:55,639+0530","service":"FyersAPIRequest","taskName":"Task-50"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:34:56,766+0530","service":"FyersAPIRequest","taskName":"Task-50"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:34:58,042+0530","service":"FyersAPIRequest","taskName":"Task-52"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:34:59,426+0530","service":"FyersAPIRequest","taskName":"Task-52"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:35:35,947+0530","service":"FyersAPIRequest","taskName":"Task-54"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:35:36,864+0530","service":"FyersAPIRequest","taskName":"Task-54"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:35:38,363+0530","service":"FyersAPIRequest","taskName":"Task-56"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:35:39,185+0530","service":"FyersAPIRequest","taskName":"Task-56"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:36:01,081+0530","service":"FyersAPIRequest","taskName":"Task-58"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:36:02,284+0530","service":"FyersAPIRequest","taskName":"Task-58"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:36:03,398+0530","service":"FyersAPIRequest","taskName":"Task-60"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:36:04,908+0530","service":"FyersAPIRequest","taskName":"Task-60"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:36:16,603+0530","service":"FyersAPIRequest","taskName":"Task-62"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:36:17,493+0530","service":"FyersAPIRequest","taskName":"Task-62"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:36:18,744+0530","service":"FyersAPIRequest","taskName":"Task-64"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:36:20,826+0530","service":"FyersAPIRequest","taskName":"Task-64"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:37:02,395+0530","service":"FyersAPIRequest","taskName":"Task-66"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:37:03,484+0530","service":"FyersAPIRequest","taskName":"Task-66"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:37:06,065+0530","service":"FyersAPIRequest","taskName":"Task-68"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:37:07,524+0530","service":"FyersAPIRequest","taskName":"Task-68"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:37:15,717+0530","service":"FyersAPIRequest","taskName":"Task-70"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:37:17,296+0530","service":"FyersAPIRequest","taskName":"Task-70"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:37:19,796+0530","service":"FyersAPIRequest","taskName":"Task-72"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:37:20,953+0530","service":"FyersAPIRequest","taskName":"Task-72"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:37:54,365+0530","service":"FyersAPIRequest","taskName":"Task-74"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:37:55,511+0530","service":"FyersAPIRequest","taskName":"Task-74"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:37:57,312+0530","service":"FyersAPIRequest","taskName":"Task-76"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:37:58,196+0530","service":"FyersAPIRequest","taskName":"Task-76"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:38:07,193+0530","service":"FyersAPIRequest","taskName":"Task-78"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:38:08,091+0530","service":"FyersAPIRequest","taskName":"Task-78"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:38:09,599+0530","service":"FyersAPIRequest","taskName":"Task-80"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:38:11,185+0530","service":"FyersAPIRequest","taskName":"Task-80"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:39:07,835+0530","service":"FyersAPIRequest","taskName":"Task-82"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:39:08,701+0530","service":"FyersAPIRequest","taskName":"Task-82"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:39:10,142+0530","service":"FyersAPIRequest","taskName":"Task-84"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:39:11,846+0530","service":"FyersAPIRequest","taskName":"Task-84"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:39:52,101+0530","service":"FyersAPIRequest","taskName":"Task-86"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:39:54,065+0530","service":"FyersAPIRequest","taskName":"Task-86"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:39:56,698+0530","service":"FyersAPIRequest","taskName":"Task-88"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:39:57,695+0530","service":"FyersAPIRequest","taskName":"Task-88"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:40:09,758+0530","service":"FyersAPIRequest","taskName":"Task-90"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:40:11,105+0530","service":"FyersAPIRequest","taskName":"Task-90"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:40:13,707+0530","service":"FyersAPIRequest","taskName":"Task-92"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:40:14,700+0530","service":"FyersAPIRequest","taskName":"Task-92"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:42:20,593+0530","service":"FyersAPIRequest","taskName":"Task-94"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:42:21,734+0530","service":"FyersAPIRequest","taskName":"Task-94"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:42:22,886+0530","service":"FyersAPIRequest","taskName":"Task-98"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:42:24,183+0530","service":"FyersAPIRequest","taskName":"Task-98"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:42:26,031+0530","service":"FyersAPIRequest","taskName":"Task-99"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:42:27,823+0530","service":"FyersAPIRequest","taskName":"Task-99"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:42:30,140+0530","service":"FyersAPIRequest","taskName":"Task-100"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:42:31,009+0530","service":"FyersAPIRequest","taskName":"Task-100"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:44:33,843+0530","service":"FyersAPIRequest","taskName":"Task-102"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:44:35,446+0530","service":"FyersAPIRequest","taskName":"Task-102"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:44:37,546+0530","service":"FyersAPIRequest","taskName":"Task-104"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:44:38,417+0530","service":"FyersAPIRequest","taskName":"Task-104"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:45:06,399+0530","service":"FyersAPIRequest","taskName":"Task-106"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:45:07,483+0530","service":"FyersAPIRequest","taskName":"Task-106"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:45:09,668+0530","service":"FyersAPIRequest","taskName":"Task-108"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:45:10,637+0530","service":"FyersAPIRequest","taskName":"Task-108"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:47:33,227+0530","service":"FyersAPIRequest","taskName":"Task-110"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:47:34,242+0530","service":"FyersAPIRequest","taskName":"Task-110"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:49:28,714+0530","service":"FyersAPIRequest","taskName":"Task-112"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:49:29,649+0530","service":"FyersAPIRequest","taskName":"Task-112"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:49:43,474+0530","service":"FyersAPIRequest","taskName":"Task-114"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:49:44,290+0530","service":"FyersAPIRequest","taskName":"Task-114"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:49:58,305+0530","service":"FyersAPIRequest","taskName":"Task-116"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:49:59,221+0530","service":"FyersAPIRequest","taskName":"Task-116"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:50:11,590+0530","service":"FyersAPIRequest","taskName":"Task-118"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:50:12,678+0530","service":"FyersAPIRequest","taskName":"Task-118"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:50:24,368+0530","service":"FyersAPIRequest","taskName":"Task-120"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:50:25,305+0530","service":"FyersAPIRequest","taskName":"Task-120"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:51:26,196+0530","service":"FyersAPIRequest","taskName":"Task-122"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:51:27,551+0530","service":"FyersAPIRequest","taskName":"Task-122"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:51:44,700+0530","service":"FyersAPIRequest","taskName":"Task-124"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:51:45,864+0530","service":"FyersAPIRequest","taskName":"Task-124"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:52:59,619+0530","service":"FyersAPIRequest","taskName":"Task-126"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:53:01,104+0530","service":"FyersAPIRequest","taskName":"Task-126"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:53:09,876+0530","service":"FyersAPIRequest","taskName":"Task-128"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:53:10,950+0530","service":"FyersAPIRequest","taskName":"Task-128"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:53:28,547+0530","service":"FyersAPIRequest","taskName":"Task-130"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:53:29,310+0530","service":"FyersAPIRequest","taskName":"Task-130"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:53:49,387+0530","service":"FyersAPIRequest","taskName":"Task-132"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:53:50,223+0530","service":"FyersAPIRequest","taskName":"Task-132"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:56:40,573+0530","service":"FyersAPIRequest","taskName":"Task-134"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:56:41,364+0530","service":"FyersAPIRequest","taskName":"Task-134"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:57:16,050+0530","service":"FyersAPIRequest","taskName":"Task-136"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:57:17,105+0530","service":"FyersAPIRequest","taskName":"Task-136"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:57:30,579+0530","service":"FyersAPIRequest","taskName":"Task-138"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:57:31,616+0530","service":"FyersAPIRequest","taskName":"Task-138"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:57:51,164+0530","service":"FyersAPIRequest","taskName":"Task-140"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:57:52,247+0530","service":"FyersAPIRequest","taskName":"Task-140"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:58:53,467+0530","service":"FyersAPIRequest","taskName":"Task-142"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:58:54,400+0530","service":"FyersAPIRequest","taskName":"Task-142"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:58:57,823+0530","service":"FyersAPIRequest","taskName":"Task-153"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:58:58,774+0530","service":"FyersAPIRequest","taskName":"Task-153"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:58:59,094+0530","service":"FyersAPIRequest","taskName":"Task-154"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:59:17,016+0530","service":"FyersAPIRequest","taskName":"Task-160"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:59:17,817+0530","service":"FyersAPIRequest","taskName":"Task-160"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:59:36,698+0530","service":"FyersAPIRequest","taskName":"Task-162"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:59:37,695+0530","service":"FyersAPIRequest","taskName":"Task-162"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:59:38,792+0530","service":"FyersAPIRequest","taskName":"Task-164"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:59:40,093+0530","service":"FyersAPIRequest","taskName":"Task-164"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 01:59:44,383+0530","service":"FyersAPIRequest","taskName":"Task-166"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 01:59:45,224+0530","service":"FyersAPIRequest","taskName":"Task-166"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:00:25,867+0530","service":"FyersAPIRequest","taskName":"Task-168"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:00:26,701+0530","service":"FyersAPIRequest","taskName":"Task-168"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:00:27,733+0530","service":"FyersAPIRequest","taskName":"Task-170"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:00:29,402+0530","service":"FyersAPIRequest","taskName":"Task-170"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:00:38,698+0530","service":"FyersAPIRequest","taskName":"Task-172"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:00:39,456+0530","service":"FyersAPIRequest","taskName":"Task-172"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:01:09,098+0530","service":"FyersAPIRequest","taskName":"Task-174"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:01:10,098+0530","service":"FyersAPIRequest","taskName":"Task-174"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:01:11,223+0530","service":"FyersAPIRequest","taskName":"Task-176"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:01:12,654+0530","service":"FyersAPIRequest","taskName":"Task-176"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:01:35,343+0530","service":"FyersAPIRequest","taskName":"Task-178"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:01:36,253+0530","service":"FyersAPIRequest","taskName":"Task-178"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:01:37,460+0530","service":"FyersAPIRequest","taskName":"Task-180"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:01:39,614+0530","service":"FyersAPIRequest","taskName":"Task-180"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:01:45,545+0530","service":"FyersAPIRequest","taskName":"Task-182"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:01:46,380+0530","service":"FyersAPIRequest","taskName":"Task-182"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:02:21,850+0530","service":"FyersAPIRequest","taskName":"Task-184"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:02:22,648+0530","service":"FyersAPIRequest","taskName":"Task-184"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:02:23,846+0530","service":"FyersAPIRequest","taskName":"Task-186"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:02:25,224+0530","service":"FyersAPIRequest","taskName":"Task-186"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:02:54,145+0530","service":"FyersAPIRequest","taskName":"Task-188"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:02:55,012+0530","service":"FyersAPIRequest","taskName":"Task-188"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:03:18,694+0530","service":"FyersAPIRequest","taskName":"Task-190"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:03:20,149+0530","service":"FyersAPIRequest","taskName":"Task-190"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:09:14,133+0530","service":"FyersAPIRequest","taskName":"Task-2252"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:09:15,014+0530","service":"FyersAPIRequest","taskName":"Task-2252"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:09:24,214+0530","service":"FyersAPIRequest","taskName":"Task-2254"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:09:25,132+0530","service":"FyersAPIRequest","taskName":"Task-2254"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:09:27,080+0530","service":"FyersAPIRequest","taskName":"Task-2256"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:09:31,029+0530","service":"FyersAPIRequest","taskName":"Task-2256"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:09:41,056+0530","service":"FyersAPIRequest","taskName":"Task-2258"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:09:42,094+0530","service":"FyersAPIRequest","taskName":"Task-2258"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:00,897+0530","service":"FyersAPIRequest","taskName":"Task-2260"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:03,238+0530","service":"FyersAPIRequest","taskName":"Task-2260"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:07,433+0530","service":"FyersAPIRequest","taskName":"Task-2263"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:11,236+0530","service":"FyersAPIRequest","taskName":"Task-2263"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:14,407+0530","service":"FyersAPIRequest","taskName":"Task-2264"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:17,040+0530","service":"FyersAPIRequest","taskName":"Task-2264"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:20,547+0530","service":"FyersAPIRequest","taskName":"Task-2265"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:22,365+0530","service":"FyersAPIRequest","taskName":"Task-2265"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:24,008+0530","service":"FyersAPIRequest","taskName":"Task-2266"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:25,006+0530","service":"FyersAPIRequest","taskName":"Task-2266"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:26,728+0530","service":"FyersAPIRequest","taskName":"Task-2267"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:29,117+0530","service":"FyersAPIRequest","taskName":"Task-2267"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:33,488+0530","service":"FyersAPIRequest","taskName":"Task-2268"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:35,479+0530","service":"FyersAPIRequest","taskName":"Task-2268"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:12:36,653+0530","service":"FyersAPIRequest","taskName":"Task-2269"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:12:38,759+0530","service":"FyersAPIRequest","taskName":"Task-2269"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:13:10,093+0530","service":"FyersAPIRequest","taskName":"Task-2271"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:13:10,916+0530","service":"FyersAPIRequest","taskName":"Task-2271"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:13:20,834+0530","service":"FyersAPIRequest","taskName":"Task-2375"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:13:21,583+0530","service":"FyersAPIRequest","taskName":"Task-2375"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:13:54,427+0530","service":"FyersAPIRequest","taskName":"Task-3007"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:13:55,881+0530","service":"FyersAPIRequest","taskName":"Task-3007"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:13:58,421+0530","service":"FyersAPIRequest","taskName":"Task-3010"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:14:00,252+0530","service":"FyersAPIRequest","taskName":"Task-3010"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:15:01,654+0530","service":"FyersAPIRequest","taskName":"Task-4337"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:15:02,565+0530","service":"FyersAPIRequest","taskName":"Task-4337"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:15:12,215+0530","service":"FyersAPIRequest","taskName":"Task-4489"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:15:13,214+0530","service":"FyersAPIRequest","taskName":"Task-4489"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:15:15,166+0530","service":"FyersAPIRequest","taskName":"Task-4493"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:15:16,028+0530","service":"FyersAPIRequest","taskName":"Task-4493"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:15:47,661+0530","service":"FyersAPIRequest","taskName":"Task-5129"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:15:49,317+0530","service":"FyersAPIRequest","taskName":"Task-5129"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:15:54,340+0530","service":"FyersAPIRequest","taskName":"Task-5215"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:15:55,143+0530","service":"FyersAPIRequest","taskName":"Task-5215"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":422,"API":"/history"},"timestamp":"2025-08-01 02:17:09,392+0530","service":"FyersAPIRequest","taskName":"Task-5938"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:17:36,645+0530","service":"FyersAPIRequest","taskName":"Task-6562"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:17:37,530+0530","service":"FyersAPIRequest","taskName":"Task-6562"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:20:39,929+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:20:41,141+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:22:35,891+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:22:37,391+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:23:47,317+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:23:48,204+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 02:23:56,063+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:24:27,798+0530","service":"FyersAPIRequest","taskName":"Task-488"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:24:28,583+0530","service":"FyersAPIRequest","taskName":"Task-488"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:26:08,839+0530","service":"FyersAPIRequest","taskName":"Task-520"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:26:09,646+0530","service":"FyersAPIRequest","taskName":"Task-520"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:26:13,594+0530","service":"FyersAPIRequest","taskName":"Task-531"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:26:14,550+0530","service":"FyersAPIRequest","taskName":"Task-531"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:26:14,841+0530","service":"FyersAPIRequest","taskName":"Task-534"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:26:22,842+0530","service":"FyersAPIRequest","taskName":"Task-538"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:26:23,746+0530","service":"FyersAPIRequest","taskName":"Task-538"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:26:48,480+0530","service":"FyersAPIRequest","taskName":"Task-540"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:26:49,281+0530","service":"FyersAPIRequest","taskName":"Task-540"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:27:49,003+0530","service":"FyersAPIRequest","taskName":"Task-1154"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:27:49,915+0530","service":"FyersAPIRequest","taskName":"Task-1154"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 02:48:55,669+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:49:15,149+0530","service":"FyersAPIRequest","taskName":"Task-59"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:49:16,027+0530","service":"FyersAPIRequest","taskName":"Task-59"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:57:57,211+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:57:58,166+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:57:59,501+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:58:00,447+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 02:59:32,405+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 02:59:33,414+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:01:06,762+0530","service":"FyersAPIRequest","taskName":"Task-1658"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:01:20,939+0530","service":"FyersAPIRequest","taskName":"Task-1709"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:01:21,899+0530","service":"FyersAPIRequest","taskName":"Task-1709"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:07:07,575+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:07:08,533+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:07:57,581+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:08:11,411+0530","service":"FyersAPIRequest","taskName":"Task-16"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:08:12,200+0530","service":"FyersAPIRequest","taskName":"Task-16"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:12:02,195+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:12:38,370+0530","service":"FyersAPIRequest","taskName":"Task-58"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:12:41,260+0530","service":"FyersAPIRequest","taskName":"Task-58"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:14:24,195+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:15:01,477+0530","service":"FyersAPIRequest","taskName":"Task-42"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:15:05,233+0530","service":"FyersAPIRequest","taskName":"Task-42"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:18:19,170+0530","service":"FyersAPIRequest","taskName":"Task-54"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:19:49,806+0530","service":"FyersAPIRequest","taskName":"Task-132"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:19:50,636+0530","service":"FyersAPIRequest","taskName":"Task-132"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:20:20,001+0530","service":"FyersAPIRequest","taskName":"Task-135"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:26:10,242+0530","service":"FyersAPIRequest","taskName":"Task-210"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:26:12,829+0530","service":"FyersAPIRequest","taskName":"Task-210"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:26:27,516+0530","service":"FyersAPIRequest","taskName":"Task-212"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:26:28,312+0530","service":"FyersAPIRequest","taskName":"Task-212"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:28:29,001+0530","service":"FyersAPIRequest","taskName":"Task-214"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:28:30,851+0530","service":"FyersAPIRequest","taskName":"Task-214"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:29:09,048+0530","service":"FyersAPIRequest","taskName":"Task-216"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:29:09,789+0530","service":"FyersAPIRequest","taskName":"Task-216"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:29:16,843+0530","service":"FyersAPIRequest","taskName":"Task-218"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:29:17,625+0530","service":"FyersAPIRequest","taskName":"Task-218"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:30:11,233+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:30:16,335+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:31:34,194+0530","service":"FyersAPIRequest","taskName":"Task-1848"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:31:35,045+0530","service":"FyersAPIRequest","taskName":"Task-1848"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 03:31:42,766+0530","service":"FyersAPIRequest","taskName":"Task-1857"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 03:32:38,066+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 03:32:39,154+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:30,971+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:31,853+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:34,428+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:35,307+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:36,272+0530","service":"FyersAPIRequest","taskName":"Task-22"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:37,108+0530","service":"FyersAPIRequest","taskName":"Task-22"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:37,374+0530","service":"FyersAPIRequest","taskName":"Task-18"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:37,625+0530","service":"FyersAPIRequest","taskName":"Task-28"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:45,029+0530","service":"FyersAPIRequest","taskName":"Task-32"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:45,974+0530","service":"FyersAPIRequest","taskName":"Task-32"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:52,073+0530","service":"FyersAPIRequest","taskName":"Task-33"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:53,011+0530","service":"FyersAPIRequest","taskName":"Task-33"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:54,076+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:56,054+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:56,664+0530","service":"FyersAPIRequest","taskName":"Task-34"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:10:57,827+0530","service":"FyersAPIRequest","taskName":"Task-45"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:59,226+0530","service":"FyersAPIRequest","taskName":"Task-45"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:10:59,234+0530","service":"FyersAPIRequest","taskName":"Task-42"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:11:00,605+0530","service":"FyersAPIRequest","taskName":"Task-47"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:11:01,461+0530","service":"FyersAPIRequest","taskName":"Task-47"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:11:02,464+0530","service":"FyersAPIRequest","taskName":"Task-52"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:11:03,395+0530","service":"FyersAPIRequest","taskName":"Task-52"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:11:03,665+0530","service":"FyersAPIRequest","taskName":"Task-49"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:11:03,973+0530","service":"FyersAPIRequest","taskName":"Task-54"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:13:53,059+0530","service":"FyersAPIRequest","taskName":"Task-2465"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:13:54,033+0530","service":"FyersAPIRequest","taskName":"Task-2465"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 12:14:02,416+0530","service":"FyersAPIRequest","taskName":"Task-2477"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:35:27,271+0530","service":"FyersAPIRequest","taskName":"Task-2480"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:35:29,406+0530","service":"FyersAPIRequest","taskName":"Task-2480"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:35:32,318+0530","service":"FyersAPIRequest","taskName":"Task-2481"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:35:37,363+0530","service":"FyersAPIRequest","taskName":"Task-2481"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:38:03,598+0530","service":"FyersAPIRequest","taskName":"Task-2928"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:38:05,542+0530","service":"FyersAPIRequest","taskName":"Task-2928"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:41:38,333+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:41:44,706+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:41:50,352+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:41:52,360+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:42:54,824+0530","service":"FyersAPIRequest","taskName":"Task-230"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:42:56,710+0530","service":"FyersAPIRequest","taskName":"Task-230"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:43:16,426+0530","service":"FyersAPIRequest","taskName":"Task-232"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:43:18,759+0530","service":"FyersAPIRequest","taskName":"Task-232"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:43:29,361+0530","service":"FyersAPIRequest","taskName":"Task-234"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:43:31,891+0530","service":"FyersAPIRequest","taskName":"Task-234"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:43:49,760+0530","service":"FyersAPIRequest","taskName":"Task-236"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:43:51,908+0530","service":"FyersAPIRequest","taskName":"Task-236"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:44:05,539+0530","service":"FyersAPIRequest","taskName":"Task-238"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:44:07,313+0530","service":"FyersAPIRequest","taskName":"Task-238"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:45:53,425+0530","service":"FyersAPIRequest","taskName":"Task-240"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:45:54,272+0530","service":"FyersAPIRequest","taskName":"Task-240"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:48:16,354+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:48:17,140+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:48:33,421+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:48:34,229+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 12:51:44,999+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:54:10,988+0530","service":"FyersAPIRequest","taskName":"Task-44"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:54:12,076+0530","service":"FyersAPIRequest","taskName":"Task-44"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 12:54:31,032+0530","service":"FyersAPIRequest","taskName":"Task-46"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 12:54:32,080+0530","service":"FyersAPIRequest","taskName":"Task-46"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:01:26,817+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:01:27,713+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 13:01:44,752+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:04:37,575+0530","service":"FyersAPIRequest","taskName":"Task-161"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:04:38,535+0530","service":"FyersAPIRequest","taskName":"Task-161"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:09:35,926+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:09:36,874+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:09:38,415+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:09:39,249+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:10:06,176+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:10:07,168+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:10:09,413+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:10:10,567+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:10:16,975+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:10:18,054+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:10:19,254+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:10:20,221+0530","service":"FyersAPIRequest","taskName":"Task-11"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:10:22,259+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:10:23,171+0530","service":"FyersAPIRequest","taskName":"Task-12"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:18:53,284+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:18:54,241+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:20:55,911+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:20:57,516+0530","service":"FyersAPIRequest","taskName":"Task-6"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:22:36,893+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:22:37,853+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:22:42,198+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:22:43,332+0530","service":"FyersAPIRequest","taskName":"Task-5"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:22:49,101+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:22:49,955+0530","service":"FyersAPIRequest","taskName":"Task-37"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 13:23:12,961+0530","service":"FyersAPIRequest","taskName":"Task-8"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:26:38,752+0530","service":"FyersAPIRequest","taskName":"Task-89"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:26:40,000+0530","service":"FyersAPIRequest","taskName":"Task-89"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:32:07,998+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:32:08,828+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 13:32:23,848+0530","service":"FyersAPIRequest","taskName":"Task-7"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:36:44,354+0530","service":"FyersAPIRequest","taskName":"Task-118"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:36:47,272+0530","service":"FyersAPIRequest","taskName":"Task-118"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:37:09,923+0530","service":"FyersAPIRequest","taskName":"Task-120"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:37:11,422+0530","service":"FyersAPIRequest","taskName":"Task-120"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:37:25,265+0530","service":"FyersAPIRequest","taskName":"Task-122"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:37:27,355+0530","service":"FyersAPIRequest","taskName":"Task-122"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-01 13:44:08,602+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-01 13:44:09,472+0530","service":"FyersAPIRequest","taskName":"Task-4"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/history"},"timestamp":"2025-08-01 13:44:20,511+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:55:22,156+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:55:23,906+0530","service":"FyersAPIRequest","taskName":"Task-10"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:55:28,384+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:55:30,331+0530","service":"FyersAPIRequest","taskName":"Task-13"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:55:33,323+0530","service":"FyersAPIRequest","taskName":"Task-28"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:55:34,462+0530","service":"FyersAPIRequest","taskName":"Task-28"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:55:35,196+0530","service":"FyersAPIRequest","taskName":"Task-23"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:55:35,652+0530","service":"FyersAPIRequest","taskName":"Task-32"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:55:56,235+0530","service":"FyersAPIRequest","taskName":"Task-36"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:56:01,293+0530","service":"FyersAPIRequest","taskName":"Task-36"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:56:51,718+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:56:53,479+0530","service":"FyersAPIRequest","taskName":"Task-38"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:56:55,624+0530","service":"FyersAPIRequest","taskName":"Task-40"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:56:57,147+0530","service":"FyersAPIRequest","taskName":"Task-40"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:56:58,265+0530","service":"FyersAPIRequest","taskName":"Task-44"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:56:59,252+0530","service":"FyersAPIRequest","taskName":"Task-44"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/profile"},"timestamp":"2025-08-02 18:57:00,419+0530","service":"FyersAPIRequest","taskName":"Task-47"}
{"level":"DEBUG","location":"[get_call:140] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:57:04,213+0530","service":"FyersAPIRequest","taskName":"Task-47"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:57:06,333+0530","service":"FyersAPIRequest","taskName":"Task-45"}
{"level":"DEBUG","location":"[get_async_call:360] fyersModel","message":{"Status Code":200,"API":"/funds"},"timestamp":"2025-08-02 18:57:08,459+0530","service":"FyersAPIRequest","taskName":"Task-81"}
